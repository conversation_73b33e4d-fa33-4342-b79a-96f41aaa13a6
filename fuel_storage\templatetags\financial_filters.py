from django import template
from decimal import Decimal

register = template.Library()

@register.filter
def percentage(value, total):
    """حساب النسبة المئوية"""
    try:
        if total and float(total) != 0:
            return round((float(value) / float(total)) * 100, 2)
        return 0
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def subtract(value, arg):
    """طرح قيمة من أخرى"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def multiply(value, arg):
    """ضرب قيمة في أخرى"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def divide(value, arg):
    """قسمة قيمة على أخرى"""
    try:
        if float(arg) != 0:
            return float(value) / float(arg)
        return 0
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def abs_value(value):
    """القيمة المطلقة"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0

@register.filter
def to_words(value):
    """تحويل الرقم إلى كلمات عربية"""
    from ..utils import number_to_arabic_words
    return number_to_arabic_words(value)
