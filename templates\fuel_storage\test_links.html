<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار روابط الطباعة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-link {
            display: block;
            padding: 15px 20px;
            margin: 10px 0;
            border: 2px solid #ddd;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-link.html-link {
            border-color: #28a745;
            background: #f8fff9;
        }
        .test-link.html-link:hover {
            background: #28a745;
            color: white;
        }
        .test-link.pdf-link {
            border-color: #dc3545;
            background: #fff8f8;
        }
        .test-link.pdf-link:hover {
            background: #dc3545;
            color: white;
        }
        .invoice-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 10px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-print text-primary"></i>
            اختبار روابط طباعة الفواتير
        </h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            هذه الصفحة لاختبار روابط الطباعة والتأكد من عملها بشكل صحيح
        </div>

        <!-- فاتورة رقم 1 -->
        <div class="invoice-section">
            <h3><i class="fas fa-file-invoice"></i> فاتورة رقم 1</h3>
            
            <a href="/financial/invoices/1/print-simple/" target="_blank" class="test-link html-link">
                <i class="fas fa-file-alt text-success me-2"></i>
                <strong>طباعة HTML - فاتورة 1</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/1/print-simple/</small>
            </a>
            
            <a href="/financial/invoices/1/print/" target="_blank" class="test-link pdf-link">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <strong>طباعة PDF - فاتورة 1</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/1/print/</small>
            </a>
        </div>

        <!-- فاتورة رقم 2 -->
        <div class="invoice-section">
            <h3><i class="fas fa-file-invoice"></i> فاتورة رقم 2</h3>
            
            <a href="/financial/invoices/2/print-simple/" target="_blank" class="test-link html-link">
                <i class="fas fa-file-alt text-success me-2"></i>
                <strong>طباعة HTML - فاتورة 2</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/2/print-simple/</small>
            </a>
            
            <a href="/financial/invoices/2/print/" target="_blank" class="test-link pdf-link">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <strong>طباعة PDF - فاتورة 2</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/2/print/</small>
            </a>
        </div>

        <!-- فاتورة رقم 3 -->
        <div class="invoice-section">
            <h3><i class="fas fa-file-invoice"></i> فاتورة رقم 3</h3>
            
            <a href="/financial/invoices/3/print-simple/" target="_blank" class="test-link html-link">
                <i class="fas fa-file-alt text-success me-2"></i>
                <strong>طباعة HTML - فاتورة 3</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/3/print-simple/</small>
            </a>
            
            <a href="/financial/invoices/3/print/" target="_blank" class="test-link pdf-link">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <strong>طباعة PDF - فاتورة 3</strong>
                <small class="d-block text-muted">الرابط: /financial/invoices/3/print/</small>
            </a>
        </div>

        <div class="text-center mt-4">
            <a href="/financial/invoices/" class="btn btn-primary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى قائمة الفواتير
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تسجيل النقرات في console
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const url = this.href;
                const type = this.classList.contains('html-link') ? 'HTML' : 'PDF';
                console.log(`تم النقر على رابط ${type}: ${url}`);
                
                // إظهار تنبيه للمستخدم
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alertDiv.style.top = '20px';
                alertDiv.style.right = '20px';
                alertDiv.style.zIndex = '9999';
                alertDiv.innerHTML = `
                    <strong>تم النقر على:</strong> ${type} - ${url}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);
                
                // إزالة التنبيه بعد 3 ثوان
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 3000);
            });
        });
    </script>
</body>
</html>
