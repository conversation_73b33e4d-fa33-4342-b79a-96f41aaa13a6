from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary,
    Storage, StorageItem, MaintenanceSchedule, QualityControl, SystemLog,
    IncomingOperation, OutgoingOperation, DamageOperation, StorageTransfer,
    IncomingOperationItem, OutgoingOperationItem, DamageOperationItem, StorageTransferItem,
    IncomingReturn, OutgoingReturn, IncomingReturnItem, OutgoingReturnItem,
    OperationModification,
    # النماذج المالية
    AccountType, Account, AccountingPeriod, JournalEntry, Transaction,
    PaymentMethod, Invoice, InvoiceItem, Payment, CashFlow, Budget, BudgetItem
)

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'full_name', 'user_type', 'is_active')
    list_filter = ('user_type', 'is_active')
    search_fields = ('username', 'full_name')
    
    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {'fields': ('full_name', 'user_type')}),
    )

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name',)
    list_per_page = 20

@admin.register(Station)
class StationAdmin(admin.ModelAdmin):
    list_display = ('name', 'address', 'created_at')
    search_fields = ('name', 'address')
    list_per_page = 20

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Beneficiary)
class BeneficiaryAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Storage)
class StorageAdmin(admin.ModelAdmin):
    list_display = ('name', 'classification', 'keeper_name', 'phone_number', 'created_at')
    list_filter = ('classification',)
    search_fields = ('name', 'keeper_name')
    list_per_page = 20

@admin.register(StorageItem)
class StorageItemAdmin(admin.ModelAdmin):
    list_display = ('storage', 'category', 'unit_of_measure', 'opening_balance', 'current_quantity', 'updated_at')
    list_filter = ('storage', 'category', 'unit_of_measure')
    search_fields = ('storage__name', 'category__name')
    list_per_page = 20

@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    list_display = ('storage', 'maintenance_type', 'scheduled_date', 'status', 'technician_name')
    list_filter = ('maintenance_type', 'status', 'scheduled_date')
    search_fields = ('storage__name', 'description')
    list_per_page = 20

@admin.register(QualityControl)
class QualityControlAdmin(admin.ModelAdmin):
    list_display = ('storage_item', 'inspection_type', 'inspection_date', 'quality_status', 'inspector_name')
    list_filter = ('inspection_type', 'quality_status', 'inspection_date')
    search_fields = ('storage_item__category__name', 'inspector_name')
    list_per_page = 20

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'model_name', 'object_id', 'timestamp')
    list_filter = ('action', 'model_name', 'timestamp')
    search_fields = ('user__username', 'description')
    list_per_page = 20
    readonly_fields = ('timestamp',)

# Inline classes for operation items
class IncomingOperationItemInline(admin.TabularInline):
    model = IncomingOperationItem
    extra = 1

class OutgoingOperationItemInline(admin.TabularInline):
    model = OutgoingOperationItem
    extra = 1

class DamageOperationItemInline(admin.TabularInline):
    model = DamageOperationItem
    extra = 1

class StorageTransferItemInline(admin.TabularInline):
    model = StorageTransferItem
    extra = 1

class IncomingReturnItemInline(admin.TabularInline):
    model = IncomingReturnItem
    extra = 1

class OutgoingReturnItemInline(admin.TabularInline):
    model = OutgoingReturnItem
    extra = 1

# Operation Admin Classes
@admin.register(IncomingOperation)
class IncomingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'supplier', 'station', 'operation_date', 'is_locked')
    list_filter = ('storage', 'supplier', 'station', 'is_locked', 'operation_date')
    search_fields = ('paper_number', 'storage__name', 'supplier__full_name')
    inlines = [IncomingOperationItemInline]
    list_per_page = 20

@admin.register(OutgoingOperation)
class OutgoingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'beneficiary', 'operation_date', 'is_locked')
    list_filter = ('storage', 'beneficiary', 'is_locked', 'operation_date')
    search_fields = ('paper_number', 'storage__name', 'beneficiary__full_name')
    inlines = [OutgoingOperationItemInline]
    list_per_page = 20

@admin.register(DamageOperation)
class DamageOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'damage_date', 'is_locked')
    list_filter = ('storage', 'is_locked', 'damage_date')
    search_fields = ('paper_number', 'storage__name')
    inlines = [DamageOperationItemInline]
    list_per_page = 20

@admin.register(StorageTransfer)
class StorageTransferAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'from_storage', 'to_storage', 'transfer_date', 'is_locked')
    list_filter = ('from_storage', 'to_storage', 'is_locked', 'transfer_date')
    search_fields = ('paper_number', 'from_storage__name', 'to_storage__name')
    inlines = [StorageTransferItemInline]
    list_per_page = 20

@admin.register(IncomingReturn)
class IncomingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'incoming_operation', 'return_date', 'is_locked')
    list_filter = ('incoming_operation__storage', 'is_locked', 'return_date')
    search_fields = ('paper_number', 'incoming_operation__paper_number')
    inlines = [IncomingReturnItemInline]
    list_per_page = 20

@admin.register(OutgoingReturn)
class OutgoingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'outgoing_operation', 'return_date', 'is_locked')
    list_filter = ('outgoing_operation__storage', 'is_locked', 'return_date')
    search_fields = ('paper_number', 'outgoing_operation__paper_number')
    inlines = [OutgoingReturnItemInline]
    list_per_page = 20

@admin.register(OperationModification)
class OperationModificationAdmin(admin.ModelAdmin):
    list_display = ('operation_type', 'storage', 'category', 'modification_date', 'modified_by')
    list_filter = ('operation_type', 'storage', 'modification_date')
    search_fields = ('storage__name', 'category__name', 'modified_by__username')
    list_per_page = 20


# ================================
# واجهات إدارة النظام المالي
# ================================

@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'account_type', 'is_active')
    list_filter = ('account_type', 'is_active')
    search_fields = ('name', 'code')
    list_per_page = 20

@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'account_type', 'current_balance', 'is_active', 'created_at')
    list_filter = ('account_type__account_type', 'is_active', 'created_at')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('current_balance', 'created_at')
    list_per_page = 20

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('account_type', 'name', 'code', 'parent_account')
        }),
        ('الأرصدة', {
            'fields': ('opening_balance', 'current_balance', 'currency')
        }),
        ('تفاصيل إضافية', {
            'fields': ('description', 'is_active', 'created_by', 'created_at')
        }),
    )

@admin.register(AccountingPeriod)
class AccountingPeriodAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_current', 'is_closed', 'created_at')
    list_filter = ('is_current', 'is_closed', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'closed_at')
    list_per_page = 20

    actions = ['close_periods']

    def close_periods(self, request, queryset):
        for period in queryset:
            if not period.is_closed:
                period.close_period(request.user)
        self.message_user(request, f"تم إغلاق {queryset.count()} فترة محاسبية")
    close_periods.short_description = "إغلاق الفترات المحددة"

class TransactionInline(admin.TabularInline):
    model = Transaction
    extra = 0
    readonly_fields = ('transaction_date',)

@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ('entry_number', 'entry_date', 'entry_type', 'total_amount', 'is_posted', 'created_by')
    list_filter = ('entry_type', 'is_posted', 'is_reversed', 'entry_date', 'accounting_period')
    search_fields = ('entry_number', 'description', 'reference_number')
    readonly_fields = ('created_at', 'posted_at')
    inlines = [TransactionInline]
    list_per_page = 20

    fieldsets = (
        ('معلومات القيد', {
            'fields': ('entry_number', 'entry_date', 'entry_type', 'description', 'reference_number')
        }),
        ('المبالغ والفترة', {
            'fields': ('total_amount', 'accounting_period')
        }),
        ('الحالة', {
            'fields': ('is_posted', 'is_reversed', 'posted_by', 'posted_at')
        }),
        ('ربط العمليات', {
            'fields': ('incoming_operation', 'outgoing_operation', 'incoming_return', 'outgoing_return'),
            'classes': ('collapse',)
        }),
        ('معلومات الإنشاء', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['post_entries', 'reverse_entries']

    def post_entries(self, request, queryset):
        count = 0
        for entry in queryset:
            if not entry.is_posted:
                try:
                    entry.post(request.user)
                    count += 1
                except Exception as e:
                    self.message_user(request, f"خطأ في ترحيل القيد {entry.entry_number}: {str(e)}", level='ERROR')
        self.message_user(request, f"تم ترحيل {count} قيد")
    post_entries.short_description = "ترحيل القيود المحددة"

    def reverse_entries(self, request, queryset):
        count = 0
        for entry in queryset:
            if entry.is_posted and not entry.is_reversed:
                try:
                    entry.reverse(request.user, "عكس من لوحة الإدارة")
                    count += 1
                except Exception as e:
                    self.message_user(request, f"خطأ في عكس القيد {entry.entry_number}: {str(e)}", level='ERROR')
        self.message_user(request, f"تم عكس {count} قيد")
    reverse_entries.short_description = "عكس القيود المحددة"

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('journal_entry', 'account', 'debit_amount', 'credit_amount', 'transaction_date')
    list_filter = ('transaction_date', 'account__account_type')
    search_fields = ('journal_entry__entry_number', 'account__name', 'description')
    readonly_fields = ('transaction_date',)
    list_per_page = 20

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'payment_type', 'account', 'is_active')
    list_filter = ('payment_type', 'is_active')
    search_fields = ('name', 'description')
    list_per_page = 20

class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 0
    readonly_fields = ('line_total', 'discount_amount', 'tax_amount', 'total_amount')

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'invoice_type', 'invoice_date', 'total_amount', 'paid_amount', 'status', 'created_by')
    list_filter = ('invoice_type', 'status', 'invoice_date', 'due_date')
    search_fields = ('invoice_number', 'supplier__full_name', 'beneficiary__full_name')
    readonly_fields = ('subtotal', 'tax_amount', 'total_amount', 'paid_amount', 'remaining_amount', 'created_at')
    inlines = [InvoiceItemInline]
    list_per_page = 20

    fieldsets = (
        ('معلومات الفاتورة', {
            'fields': ('invoice_number', 'invoice_type', 'invoice_date', 'due_date', 'status')
        }),
        ('العميل/المورد', {
            'fields': ('supplier', 'beneficiary')
        }),
        ('المبالغ', {
            'fields': ('subtotal', 'tax_amount', 'discount_amount', 'total_amount', 'paid_amount', 'remaining_amount')
        }),
        ('ربط العمليات', {
            'fields': ('incoming_operation', 'outgoing_operation', 'incoming_return', 'outgoing_return'),
            'classes': ('collapse',)
        }),
        ('تفاصيل إضافية', {
            'fields': ('notes', 'terms_and_conditions', 'created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_paid', 'mark_as_sent']

    def mark_as_paid(self, request, queryset):
        queryset.update(status='paid')
        self.message_user(request, f"تم تحديث حالة {queryset.count()} فاتورة إلى مدفوعة")
    mark_as_paid.short_description = "تحديد كمدفوعة"

    def mark_as_sent(self, request, queryset):
        queryset.update(status='sent')
        self.message_user(request, f"تم تحديث حالة {queryset.count()} فاتورة إلى مرسلة")
    mark_as_sent.short_description = "تحديد كمرسلة"

@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    list_display = ('invoice', 'category', 'quantity', 'unit_price', 'total_amount')
    list_filter = ('invoice__invoice_type', 'category')
    search_fields = ('invoice__invoice_number', 'category__name')
    readonly_fields = ('line_total', 'discount_amount', 'tax_amount', 'total_amount')
    list_per_page = 20

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_number', 'payment_type', 'payment_date', 'amount', 'payment_method', 'created_by')
    list_filter = ('payment_type', 'payment_date', 'payment_method')
    search_fields = ('payment_number', 'invoice__invoice_number', 'supplier__full_name', 'beneficiary__full_name')
    readonly_fields = ('created_at',)
    list_per_page = 20

    fieldsets = (
        ('معلومات الدفعة', {
            'fields': ('payment_number', 'payment_type', 'payment_date', 'amount', 'payment_method')
        }),
        ('المرجع', {
            'fields': ('invoice', 'supplier', 'beneficiary')
        }),
        ('تفاصيل إضافية', {
            'fields': ('reference_number', 'notes', 'created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(CashFlow)
class CashFlowAdmin(admin.ModelAdmin):
    list_display = ('date', 'flow_type', 'category', 'amount', 'account', 'created_by')
    list_filter = ('flow_type', 'category', 'date', 'account__account_type')
    search_fields = ('description', 'account__name')
    readonly_fields = ('created_at',)
    list_per_page = 20

class BudgetItemInline(admin.TabularInline):
    model = BudgetItem
    extra = 0
    readonly_fields = ('get_actual_amount', 'get_variance', 'get_variance_percentage')

    def get_actual_amount(self, obj):
        return obj.get_actual_amount() if obj.pk else 0
    get_actual_amount.short_description = 'المبلغ الفعلي'

    def get_variance(self, obj):
        return obj.get_variance() if obj.pk else 0
    get_variance.short_description = 'الانحراف'

    def get_variance_percentage(self, obj):
        return f"{obj.get_variance_percentage():.2f}%" if obj.pk else "0%"
    get_variance_percentage.short_description = 'نسبة الانحراف'

@admin.register(Budget)
class BudgetAdmin(admin.ModelAdmin):
    list_display = ('name', 'fiscal_year', 'start_date', 'end_date', 'total_budget', 'is_active')
    list_filter = ('fiscal_year', 'is_active', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at',)
    inlines = [BudgetItemInline]
    list_per_page = 20

@admin.register(BudgetItem)
class BudgetItemAdmin(admin.ModelAdmin):
    list_display = ('budget', 'account', 'budgeted_amount', 'get_actual_amount', 'get_variance')
    list_filter = ('budget__fiscal_year', 'account__account_type')
    search_fields = ('budget__name', 'account__name')
    readonly_fields = ('get_actual_amount', 'get_variance', 'get_variance_percentage')
    list_per_page = 20

    def get_actual_amount(self, obj):
        return obj.get_actual_amount()
    get_actual_amount.short_description = 'المبلغ الفعلي'

    def get_variance(self, obj):
        return obj.get_variance()
    get_variance.short_description = 'الانحراف'

    def get_variance_percentage(self, obj):
        return f"{obj.get_variance_percentage():.2f}%"
    get_variance_percentage.short_description = 'نسبة الانحراف'

