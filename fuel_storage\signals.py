from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.utils import timezone
from datetime import timedelta
from .models import *

@receiver(post_save, sender=StorageItem)
def check_stock_levels(sender, instance, **kwargs):
    """فحص مستويات المخزون وإنشاء التنبيهات"""
    
    # تنبيه المخزون المنخفض
    if hasattr(instance, 'is_low_stock') and instance.is_low_stock() and instance.current_quantity > 0:
        StockAlert.objects.get_or_create(
            storage_item=instance,
            alert_type='low_stock',
            defaults={
                'priority': 'medium',
                'message': f'مخزون منخفض لـ {instance.category.name} في {instance.storage.name}. الكمية الحالية: {instance.current_quantity}'
            }
        )
    
    # تنبيه نفاد المخزون
    if instance.current_quantity <= 0:
        StockAlert.objects.get_or_create(
            storage_item=instance,
            alert_type='out_of_stock',
            defaults={
                'priority': 'high',
                'message': f'نفد مخزون {instance.category.name} في {instance.storage.name}'
            }
        )
    
    # تنبيه انتهاء الصلاحية
    if hasattr(instance, 'is_expired') and instance.is_expired():
        StockAlert.objects.get_or_create(
            storage_item=instance,
            alert_type='expired',
            defaults={
                'priority': 'critical',
                'message': f'انتهت صلاحية {instance.category.name} في {instance.storage.name}'
            }
        )
    elif instance.expiry_date and hasattr(instance, 'days_until_expiry') and instance.days_until_expiry() and instance.days_until_expiry() <= 30:
        StockAlert.objects.get_or_create(
            storage_item=instance,
            alert_type='expiry_warning',
            defaults={
                'priority': 'medium',
                'message': f'تحذير: سينتهي صلاحية {instance.category.name} في {instance.storage.name} خلال {instance.days_until_expiry()} يوم'
            }
        )
    
    # تنبيه المخزون الزائد
    if instance.category.maximum_stock_level > 0 and instance.current_quantity > instance.category.maximum_stock_level:
        StockAlert.objects.get_or_create(
            storage_item=instance,
            alert_type='overstock',
            defaults={
                'priority': 'low',
                'message': f'مخزون زائد لـ {instance.category.name} في {instance.storage.name}. الكمية الحالية: {instance.current_quantity}'
            }
        )

@receiver(post_save, sender=IncomingOperation)
def log_incoming_operation(sender, instance, created, **kwargs):
    """تسجيل عمليات الوارد في سجل النظام"""
    if created:
        SystemLog.objects.create(
            user=instance.created_by,
            action='create',
            model_name='IncomingOperation',
            object_id=str(instance.id),
            description=f'إنشاء عملية وارد جديدة: {instance.paper_number}'
        )

@receiver(post_save, sender=OutgoingOperation)
def log_outgoing_operation(sender, instance, created, **kwargs):
    """تسجيل عمليات الصادر في سجل النظام"""
    if created:
        SystemLog.objects.create(
            user=instance.created_by,
            action='create',
            model_name='OutgoingOperation',
            object_id=str(instance.id),
            description=f'إنشاء عملية صادر جديدة: {instance.paper_number}'
        )

@receiver(post_save, sender=DamageOperation)
def log_damage_operation(sender, instance, created, **kwargs):
    """تسجيل عمليات التلف في سجل النظام"""
    if created:
        SystemLog.objects.create(
            user=instance.created_by,
            action='create',
            model_name='DamageOperation',
            object_id=str(instance.id),
            description=f'إنشاء عملية تلف جديدة: {instance.paper_number}'
        )

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """تسجيل دخول المستخدمين"""
    ip_address = request.META.get('REMOTE_ADDR')
    user_agent = request.META.get('HTTP_USER_AGENT')
    
    SystemLog.objects.create(
        user=user,
        action='login',
        model_name='User',
        object_id=str(user.id),
        description=f'تسجيل دخول المستخدم: {user.username}',
        ip_address=ip_address,
        user_agent=user_agent
    )

@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """تسجيل خروج المستخدمين"""
    if user:
        ip_address = request.META.get('REMOTE_ADDR')
        user_agent = request.META.get('HTTP_USER_AGENT')
        
        SystemLog.objects.create(
            user=user,
            action='logout',
            model_name='User',
            object_id=str(user.id),
            description=f'تسجيل خروج المستخدم: {user.username}',
            ip_address=ip_address,
            user_agent=user_agent
        )

@receiver(post_save, sender=MaintenanceSchedule)
def create_maintenance_alert(sender, instance, created, **kwargs):
    """إنشاء تنبيه للصيانة المتأخرة"""
    if hasattr(instance, 'is_overdue') and instance.is_overdue() and instance.status == 'scheduled':
        # البحث عن صنف في المخزن لإنشاء التنبيه
        storage_item = instance.storage.storageitem_set.first()
        if storage_item:
            StockAlert.objects.get_or_create(
                storage_item=storage_item,
                alert_type='low_stock',  # استخدام نوع موجود
                defaults={
                    'priority': 'high',
                    'message': f'صيانة متأخرة للمخزن {instance.storage.name} - {instance.get_maintenance_type_display()}'
                }
            )


# ================================
# إشارات النظام المالي
# ================================

def create_purchase_journal_entry(operation, invoice):
    """إنشاء قيد محاسبي لعملية الشراء"""
    try:
        # الحصول على الحسابات المطلوبة
        inventory_account = Account.objects.filter(code='1140').first()  # المخزون
        suppliers_account = Account.objects.filter(code='2110').first()  # الموردين

        if not inventory_account or not suppliers_account:
            return

        # الحصول على الفترة المحاسبية الحالية
        current_period = AccountingPeriod.objects.filter(is_current=True).first()
        if not current_period:
            return

        # إنشاء قيد اليومية
        entry = JournalEntry.objects.create(
            entry_number=f"PUR-{operation.id}",
            entry_date=operation.operation_date.date(),
            entry_type='automatic',
            description=f"شراء مواد - عملية رقم {operation.paper_number}",
            reference_number=operation.paper_number,
            total_amount=invoice.total_amount,
            accounting_period=current_period,
            incoming_operation=operation,
            created_by=operation.created_by
        )

        # إنشاء المعاملات
        Transaction.objects.create(
            journal_entry=entry,
            account=inventory_account,
            description=f"شراء مواد من {operation.supplier.full_name}",
            debit_amount=invoice.total_amount,
            credit_amount=0,
            transaction_date=operation.operation_date.date()
        )

        Transaction.objects.create(
            journal_entry=entry,
            account=suppliers_account,
            description=f"مستحق للمورد {operation.supplier.full_name}",
            debit_amount=0,
            credit_amount=invoice.total_amount,
            transaction_date=operation.operation_date.date()
        )

        # ترحيل القيد
        entry.post(operation.created_by)

    except Exception as e:
        print(f"خطأ في إنشاء قيد المشتريات: {str(e)}")


def create_sales_journal_entry(operation, invoice):
    """إنشاء قيد محاسبي لعملية البيع"""
    try:
        # الحصول على الحسابات المطلوبة
        customers_account = Account.objects.filter(code='1130').first()  # العملاء
        sales_account = Account.objects.filter(code='4110').first()  # المبيعات

        if not customers_account or not sales_account:
            return

        # الحصول على الفترة المحاسبية الحالية
        current_period = AccountingPeriod.objects.filter(is_current=True).first()
        if not current_period:
            return

        # إنشاء قيد اليومية للمبيعات
        entry = JournalEntry.objects.create(
            entry_number=f"SAL-{operation.id}",
            entry_date=operation.operation_date.date(),
            entry_type='automatic',
            description=f"بيع مواد - عملية رقم {operation.paper_number}",
            reference_number=operation.paper_number,
            total_amount=invoice.total_amount,
            accounting_period=current_period,
            outgoing_operation=operation,
            created_by=operation.created_by
        )

        # إنشاء المعاملات
        Transaction.objects.create(
            journal_entry=entry,
            account=customers_account,
            description=f"مبيعات للعميل {operation.beneficiary.full_name}",
            debit_amount=invoice.total_amount,
            credit_amount=0,
            transaction_date=operation.operation_date.date()
        )

        Transaction.objects.create(
            journal_entry=entry,
            account=sales_account,
            description=f"مبيعات للعميل {operation.beneficiary.full_name}",
            debit_amount=0,
            credit_amount=invoice.total_amount,
            transaction_date=operation.operation_date.date()
        )

        # ترحيل القيد
        entry.post(operation.created_by)

    except Exception as e:
        print(f"خطأ في إنشاء قيد المبيعات: {str(e)}")

