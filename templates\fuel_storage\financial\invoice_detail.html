{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الفاتورة - {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
    .invoice-detail-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .invoice-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .invoice-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .invoice-type-badge {
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .type-sales {
        background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
        color: #27ae60;
    }
    
    .type-purchase {
        background: linear-gradient(135deg, #fadbd8 0%, #f5b7b1 100%);
        color: #e74c3c;
    }
    
    .invoice-amount {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        margin: 30px 0;
        color: #2c5aa0;
    }
    
    .detail-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .section-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-left: 10px;
        color: #667eea;
    }
    
    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .detail-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .detail-label {
        font-size: 0.9rem;
        color: #7f8c8d;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .detail-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .items-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 10px;
        text-align: center;
        font-weight: 600;
    }
    
    .items-table td {
        padding: 12px 10px;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    
    .items-table tbody tr:nth-child(even) {
        background: #f8f9fa;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn-action {
        padding: 12px 25px;
        border-radius: 10px;
        border: none;
        font-weight: bold;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-print {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
    }
    
    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        color: white;
    }
    
    .btn-back:hover {
        background: #5a6268;
        color: white;
    }
    
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-draft { background: #ffeaa7; color: #d63031; }
    .status-sent { background: #74b9ff; color: #0984e3; }
    .status-paid { background: #00b894; color: white; }
    .status-partially-paid { background: #fdcb6e; color: #e17055; }
    .status-overdue { background: #e17055; color: white; }
    .status-cancelled { background: #636e72; color: white; }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .invoice-detail-container {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-file-invoice me-2"></i>
                        تفاصيل الفاتورة
                    </h1>
                    <p class="text-muted">عرض تفاصيل الفاتورة {{ invoice.invoice_number }}</p>
                </div>
                <div>
                    <a href="{% url 'fuel_storage:invoices_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة الفواتير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Details -->
    <div class="invoice-detail-container">
        <div class="invoice-header">
            <div class="invoice-number">{{ invoice.invoice_number }}</div>
            <span class="invoice-type-badge type-{{ invoice.invoice_type }}">
                {{ invoice.get_invoice_type_display }}
            </span>
            <div class="mt-2">
                <span class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </span>
            </div>
        </div>

        <!-- Amount -->
        <div class="invoice-amount">
            {{ invoice.total_amount|floatformat:2 }} ريال
        </div>

        <!-- Basic Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">تاريخ الفاتورة</div>
                    <div class="detail-value">{{ invoice.invoice_date|date:"j F Y" }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">تاريخ الاستحقاق</div>
                    <div class="detail-value">{{ invoice.due_date|date:"j F Y" }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">المبلغ المدفوع</div>
                    <div class="detail-value">{{ invoice.paid_amount|floatformat:2 }} ريال</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">المبلغ المتبقي</div>
                    <div class="detail-value">{{ invoice.remaining_amount|floatformat:2 }} ريال</div>
                </div>
            </div>
        </div>

        <!-- Customer/Supplier Information -->
        {% if invoice.supplier or invoice.beneficiary %}
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-user"></i>
                {% if invoice.supplier %}معلومات المورد{% else %}معلومات العميل{% endif %}
            </div>
            
            <div class="detail-grid">
                {% if invoice.supplier %}
                    <div class="detail-item">
                        <div class="detail-label">اسم المورد</div>
                        <div class="detail-value">{{ invoice.supplier.full_name }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف</div>
                        <div class="detail-value">{{ invoice.supplier.phone_number|default:"غير محدد" }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">البريد الإلكتروني</div>
                        <div class="detail-value">{{ invoice.supplier.email|default:"غير محدد" }}</div>
                    </div>
                {% elif invoice.beneficiary %}
                    <div class="detail-item">
                        <div class="detail-label">اسم العميل</div>
                        <div class="detail-value">{{ invoice.beneficiary.full_name }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف</div>
                        <div class="detail-value">{{ invoice.beneficiary.phone_number|default:"غير محدد" }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">البريد الإلكتروني</div>
                        <div class="detail-value">{{ invoice.beneficiary.email|default:"غير محدد" }}</div>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Invoice Items -->
        {% if items %}
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-list"></i>
                بنود الفاتورة
            </div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الصنف</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>الخصم %</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.category.name }}</td>
                        <td>{{ item.description|default:"بدون وصف" }}</td>
                        <td>{{ item.quantity|floatformat:2 }}</td>
                        <td>{{ item.unit_price|floatformat:2 }}</td>
                        <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                        <td>{{ item.total_amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- Additional Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-user-cog"></i>
                معلومات إضافية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">أنشئت بواسطة</div>
                    <div class="detail-value">{{ invoice.created_by.full_name }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">تاريخ الإنشاء</div>
                    <div class="detail-value">{{ invoice.created_at|date:"j F Y - H:i" }}</div>
                </div>
                
                {% if invoice.notes %}
                <div class="detail-item" style="grid-column: 1 / -1;">
                    <div class="detail-label">ملاحظات</div>
                    <div class="detail-value">{{ invoice.notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons no-print">
            <div class="btn-group" role="group">
                <button type="button" class="btn-action btn-print dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item text-success" href="{% url 'fuel_storage:print_invoice_simple' invoice.id %}" target="_blank"
                           onclick="console.log('طباعة HTML - الرابط: {{ request.build_absolute_uri }}{% url 'fuel_storage:print_invoice_simple' invoice.id %}')">
                            <i class="fas fa-file-alt me-2 text-success"></i>
                            <strong>طباعة HTML (موصى به للعربية)</strong>
                            <small class="d-block text-muted">الرابط: /financial/invoices/{{ invoice.id }}/print-simple/</small>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item text-danger" href="{% url 'fuel_storage:print_invoice_pdf' invoice.id %}" target="_blank"
                           onclick="console.log('طباعة PDF - الرابط: {{ request.build_absolute_uri }}{% url 'fuel_storage:print_invoice_pdf' invoice.id %}')">
                            <i class="fas fa-file-pdf me-2 text-danger"></i>
                            <strong>طباعة PDF</strong>
                            <small class="d-block text-muted">الرابط: /financial/invoices/{{ invoice.id }}/print/</small>
                        </a>
                    </li>
                </ul>
            </div>

            <a href="{% url 'admin:fuel_storage_invoice_change' invoice.id %}" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>

            <a href="{% url 'fuel_storage:invoices_list' %}" class="btn-action btn-back">
                <i class="fas fa-arrow-right"></i>
                العودة
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للعناصر
        const detailItems = document.querySelectorAll('.detail-item');
        detailItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `تفاصيل الفاتورة - {{ invoice.invoice_number }}`;
    });
</script>
{% endblock %}
