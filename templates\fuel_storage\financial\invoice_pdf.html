<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', 'Arial Unicode MS', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            background: white;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        /* Header */
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c5aa0;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: 700;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
        }
        
        .invoice-title {
            text-align: center;
            flex: 1;
        }
        
        .invoice-title h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        
        .invoice-number {
            font-size: 16px;
            font-weight: 600;
            color: #e74c3c;
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 5px;
            display: inline-block;
        }
        
        /* Invoice Info */
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .info-section {
            flex: 1;
        }
        
        .info-section h3 {
            font-size: 14px;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 10px;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 8px;
            display: flex;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            min-width: 80px;
            margin-left: 10px;
        }
        
        .info-value {
            color: #333;
        }
        
        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .items-table th {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background: #e3f2fd;
        }
        
        .description-cell {
            text-align: right !important;
            max-width: 200px;
        }
        
        .amount-cell {
            font-weight: 600;
            color: #2c5aa0;
        }
        
        /* Totals */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }
        
        .totals-table {
            width: 300px;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .totals-table tr {
            border-bottom: 1px solid #eee;
        }
        
        .totals-table tr:last-child {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            font-weight: 700;
            font-size: 14px;
        }
        
        .totals-table td {
            padding: 12px 15px;
        }
        
        .totals-label {
            font-weight: 600;
            text-align: right;
        }
        
        .totals-value {
            text-align: left;
            font-weight: 600;
            color: #2c5aa0;
        }
        
        .totals-table tr:last-child .totals-value {
            color: white;
        }
        
        /* Footer */
        .invoice-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }
        
        .terms {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .terms h4 {
            color: #2c5aa0;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .terms p {
            font-size: 11px;
            color: #666;
            line-height: 1.5;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-bottom: 10px;
            height: 50px;
        }
        
        .signature-label {
            font-weight: 600;
            color: #555;
        }
        
        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-draft { background: #ffeaa7; color: #d63031; }
        .status-sent { background: #74b9ff; color: #0984e3; }
        .status-paid { background: #00b894; color: white; }
        .status-partially-paid { background: #fdcb6e; color: #e17055; }
        .status-overdue { background: #e17055; color: white; }
        .status-cancelled { background: #636e72; color: white; }
        
        /* Print Styles */
        @media print {
            body {
                font-size: 11px;
            }
            
            .invoice-container {
                padding: 10px;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        /* RTL Adjustments */
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        
        .invoice-type {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .amount-words {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #2c5aa0;
        }
        
        .amount-words strong {
            color: #2c5aa0;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-name">{{ company_name }}</div>
                <div class="company-details">
                    {{ company_address }}<br>
                    هاتف: {{ company_phone }}<br>
                    البريد الإلكتروني: {{ company_email }}
                </div>
            </div>
            
            <div class="invoice-title">
                <h1>فاتورة</h1>
                <div class="invoice-number">{{ invoice.invoice_number }}</div>
                <div class="invoice-type">{{ invoice.get_invoice_type_display }}</div>
            </div>
            
            <div class="status-section">
                <span class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </span>
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <div class="info-section">
                <h3>معلومات الفاتورة</h3>
                <div class="info-item">
                    <span class="info-label">تاريخ الفاتورة:</span>
                    <span class="info-value">{{ invoice.invoice_date|date:"j F Y" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الاستحقاق:</span>
                    <span class="info-value">{{ invoice.due_date|date:"j F Y" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">أنشئت بواسطة:</span>
                    <span class="info-value">{{ invoice.created_by.full_name }}</span>
                </div>
            </div>
            
            <div class="info-section">
                <h3>
                    {% if invoice.invoice_type == 'purchase' %}
                        معلومات المورد
                    {% else %}
                        معلومات العميل
                    {% endif %}
                </h3>
                {% if invoice.supplier %}
                    <div class="info-item">
                        <span class="info-label">الاسم:</span>
                        <span class="info-value">{{ invoice.supplier.full_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الهاتف:</span>
                        <span class="info-value">{{ invoice.supplier.phone|default:"غير محدد" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">البريد:</span>
                        <span class="info-value">{{ invoice.supplier.email|default:"غير محدد" }}</span>
                    </div>
                {% elif invoice.beneficiary %}
                    <div class="info-item">
                        <span class="info-label">الاسم:</span>
                        <span class="info-value">{{ invoice.beneficiary.full_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الهاتف:</span>
                        <span class="info-value">{{ invoice.beneficiary.phone|default:"غير محدد" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">البريد:</span>
                        <span class="info-value">{{ invoice.beneficiary.email|default:"غير محدد" }}</span>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Items Table -->
        {% if items %}
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 25%;">الصنف</th>
                    <th style="width: 30%;">الوصف</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 12%;">سعر الوحدة</th>
                    <th style="width: 8%;">الخصم %</th>
                    <th style="width: 10%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.category.name }}</td>
                    <td class="description-cell">{{ item.description|default:"بدون وصف" }}</td>
                    <td>{{ item.quantity|floatformat:2 }}</td>
                    <td>{{ item.unit_price|floatformat:2 }}</td>
                    <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                    <td class="amount-cell">{{ item.total_amount|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}

        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="totals-label">المجموع الفرعي:</td>
                    <td class="totals-value">{{ invoice.subtotal|floatformat:2 }} ريال</td>
                </tr>
                {% if invoice.discount_amount > 0 %}
                <tr>
                    <td class="totals-label">الخصم:</td>
                    <td class="totals-value">{{ invoice.discount_amount|floatformat:2 }} ريال</td>
                </tr>
                {% endif %}
                {% if invoice.tax_amount > 0 %}
                <tr>
                    <td class="totals-label">الضريبة:</td>
                    <td class="totals-value">{{ invoice.tax_amount|floatformat:2 }} ريال</td>
                </tr>
                {% endif %}
                <tr>
                    <td class="totals-label">الإجمالي:</td>
                    <td class="totals-value">{{ invoice.total_amount|floatformat:2 }} ريال</td>
                </tr>
            </table>
        </div>

        <!-- Amount in Words -->
        {% load financial_filters %}
        <div class="amount-words">
            <strong>المبلغ بالكلمات:</strong>
            {{ invoice.total_amount|to_words }}
        </div>

        <!-- Footer -->
        <div class="invoice-footer">
            {% if invoice.terms_and_conditions %}
            <div class="terms">
                <h4>الشروط والأحكام:</h4>
                <p>{{ invoice.terms_and_conditions }}</p>
            </div>
            {% endif %}
            
            {% if invoice.notes %}
            <div class="terms">
                <h4>ملاحظات:</h4>
                <p>{{ invoice.notes }}</p>
            </div>
            {% endif %}
            
            <!-- Signatures -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">توقيع المورد</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">توقيع المستلم</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
