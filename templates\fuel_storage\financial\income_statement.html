{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الدخل{% endblock %}

{% block extra_css %}
<style>
    .income-statement-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .statement-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .statement-title {
        font-size: 2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .statement-period {
        font-size: 1.1rem;
        color: #7f8c8d;
    }
    
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .revenue-section .section-header {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .expense-section .section-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .cogs-section .section-header {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    }
    
    .account-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #ecf0f1;
        transition: background-color 0.3s ease;
    }
    
    .account-row:hover {
        background-color: #f8f9fa;
    }
    
    .account-row:last-child {
        border-bottom: none;
    }
    
    .account-name {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .account-balance {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .balance-positive {
        color: #27ae60;
    }
    
    .balance-negative {
        color: #e74c3c;
    }
    
    .subtotal-row {
        background: #f8f9fa;
        border-top: 2px solid #bdc3c7;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .total-row {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: bold;
        font-size: 1.3rem;
        border-radius: 10px;
        margin-top: 10px;
    }
    
    .profit-positive {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .profit-negative {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .summary-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .summary-card:hover {
        transform: translateY(-5px);
    }
    
    .summary-card .icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    
    .summary-card .value {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .summary-card .label {
        color: #7f8c8d;
        font-size: 1rem;
    }
    
    .revenue-card .icon { color: #27ae60; }
    .expense-card .icon { color: #e74c3c; }
    .profit-card .icon { color: #3498db; }
    .margin-card .icon { color: #f39c12; }
    
    @media print {
        .no-print { display: none !important; }
        .income-statement-container { box-shadow: none; border: 1px solid #ddd; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-chart-bar me-2"></i>
                        قائمة الدخل
                    </h1>
                    <p class="text-muted">عرض الإيرادات والمصروفات والأرباح للفترة المحددة</p>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-primary me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="row mb-4 no-print">
        <div class="col-md-6">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label for="from_date" class="form-label">من تاريخ</label>
                    <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-5">
                    <label for="to_date" class="form-label">إلى تاريخ</label>
                    <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards no-print">
        <div class="summary-card revenue-card">
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
            <div class="value balance-positive">{{ total_revenue|floatformat:2 }}</div>
            <div class="label">إجمالي الإيرادات</div>
        </div>
        
        <div class="summary-card expense-card">
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
            <div class="value balance-negative">{{ total_expenses|floatformat:2 }}</div>
            <div class="label">إجمالي المصروفات</div>
        </div>
        
        <div class="summary-card profit-card">
            <div class="icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="value {% if gross_profit >= 0 %}balance-positive{% else %}balance-negative{% endif %}">
                {{ gross_profit|floatformat:2 }}
            </div>
            <div class="label">إجمالي الربح</div>
        </div>
        
        <div class="summary-card margin-card">
            <div class="icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="value {% if net_profit >= 0 %}balance-positive{% else %}balance-negative{% endif %}">
                {{ net_profit|floatformat:2 }}
            </div>
            <div class="label">صافي الربح</div>
        </div>
    </div>

    <!-- Income Statement -->
    <div class="income-statement-container">
        <div class="statement-header">
            <div class="statement-title">قائمة الدخل</div>
            <div class="statement-period">
                للفترة من {{ from_date|date:"j F Y" }} إلى {{ to_date|date:"j F Y" }}
            </div>
        </div>

        <!-- Revenue Section -->
        <div class="revenue-section mb-4">
            <div class="section-header">
                <i class="fas fa-arrow-up me-2"></i>
                الإيرادات
            </div>
            
            {% for revenue in revenues %}
            <div class="account-row">
                <div class="account-name">{{ revenue.account.name }}</div>
                <div class="account-balance balance-positive">
                    {{ revenue.balance|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                لا توجد إيرادات في هذه الفترة
            </div>
            {% endfor %}
            
            <div class="account-row subtotal-row">
                <div class="account-name">إجمالي الإيرادات</div>
                <div class="account-balance balance-positive">
                    {{ total_revenue|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Cost of Goods Sold -->
        {% if cogs %}
        <div class="cogs-section mb-4">
            <div class="section-header">
                <i class="fas fa-box me-2"></i>
                تكلفة البضاعة المباعة
            </div>
            
            {% for cog in cogs %}
            <div class="account-row">
                <div class="account-name">{{ cog.account.name }}</div>
                <div class="account-balance balance-negative">
                    {{ cog.balance|floatformat:2 }}
                </div>
            </div>
            {% endfor %}
            
            <div class="account-row subtotal-row">
                <div class="account-name">إجمالي تكلفة البضاعة المباعة</div>
                <div class="account-balance balance-negative">
                    {{ total_cogs|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Gross Profit -->
        <div class="account-row total-row {% if gross_profit >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
            <div class="account-name">إجمالي الربح</div>
            <div class="account-balance">
                {{ gross_profit|floatformat:2 }}
            </div>
        </div>
        {% endif %}

        <!-- Expenses Section -->
        <div class="expense-section mt-4">
            <div class="section-header">
                <i class="fas fa-arrow-down me-2"></i>
                المصروفات
            </div>
            
            {% for expense in expenses %}
            <div class="account-row">
                <div class="account-name">{{ expense.account.name }}</div>
                <div class="account-balance balance-negative">
                    {{ expense.balance|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                لا توجد مصروفات في هذه الفترة
            </div>
            {% endfor %}
            
            <div class="account-row subtotal-row">
                <div class="account-name">إجمالي المصروفات</div>
                <div class="account-balance balance-negative">
                    {{ total_expenses|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Net Profit -->
        <div class="account-row total-row {% if net_profit >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
            <div class="account-name">صافي الربح</div>
            <div class="account-balance">
                {{ net_profit|floatformat:2 }}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للصفوف
        const rows = document.querySelectorAll('.account-row:not(.subtotal-row):not(.total-row)');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `قائمة الدخل - {{ from_date|date:"Y/m/d" }} إلى {{ to_date|date:"Y/m/d" }}`;
    });
</script>
{% endblock %}
