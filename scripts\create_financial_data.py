# -*- coding: utf-8 -*-
import os
import sys
import django
from datetime import datetime

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuel_storage_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from fuel_storage.models import AccountType, Account, AccountingPeriod, PaymentMethod

User = get_user_model()

def create_financial_data():
    print("Creating financial initial data...")
    
    # Get or create admin user
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = User.objects.create_user(
            username='admin',
            password='admin123',
            full_name='System Admin',
            user_type='manager',
            is_superuser=True,
            is_staff=True
        )
    
    # Create Account Types
    account_types = [
        {'name': 'Current Assets', 'code': '1100', 'account_type': 'asset'},
        {'name': 'Fixed Assets', 'code': '1200', 'account_type': 'asset'},
        {'name': 'Current Liabilities', 'code': '2100', 'account_type': 'liability'},
        {'name': 'Long-term Liabilities', 'code': '2200', 'account_type': 'liability'},
        {'name': 'Capital', 'code': '3100', 'account_type': 'equity'},
        {'name': 'Retained Earnings', 'code': '3200', 'account_type': 'equity'},
        {'name': 'Sales Revenue', 'code': '4100', 'account_type': 'revenue'},
        {'name': 'Other Revenue', 'code': '4200', 'account_type': 'revenue'},
        {'name': 'Operating Expenses', 'code': '5100', 'account_type': 'expense'},
        {'name': 'Administrative Expenses', 'code': '5200', 'account_type': 'expense'},
        {'name': 'Cost of Goods Sold', 'code': '6100', 'account_type': 'cost_of_goods'},
    ]
    
    for data in account_types:
        account_type, created = AccountType.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        if created:
            print(f"Created account type: {account_type.name}")
    
    # Create Accounts
    accounts = [
        {'name': 'Cash', 'code': '1110', 'account_type_code': '1100', 'opening_balance': 50000},
        {'name': 'Bank Account', 'code': '1120', 'account_type_code': '1100', 'opening_balance': 100000},
        {'name': 'Accounts Receivable', 'code': '1130', 'account_type_code': '1100', 'opening_balance': 0},
        {'name': 'Inventory', 'code': '1140', 'account_type_code': '1100', 'opening_balance': 200000},
        {'name': 'Equipment', 'code': '1210', 'account_type_code': '1200', 'opening_balance': 300000},
        {'name': 'Accounts Payable', 'code': '2110', 'account_type_code': '2100', 'opening_balance': 0},
        {'name': 'Accrued Expenses', 'code': '2120', 'account_type_code': '2100', 'opening_balance': 0},
        {'name': 'Capital Stock', 'code': '3110', 'account_type_code': '3100', 'opening_balance': 500000},
        {'name': 'Retained Earnings', 'code': '3210', 'account_type_code': '3200', 'opening_balance': 150000},
        {'name': 'Fuel Sales', 'code': '4110', 'account_type_code': '4100', 'opening_balance': 0},
        {'name': 'Operating Expenses', 'code': '5110', 'account_type_code': '5100', 'opening_balance': 0},
        {'name': 'Cost of Fuel Sold', 'code': '6110', 'account_type_code': '6100', 'opening_balance': 0},
    ]
    
    for data in accounts:
        account_type = AccountType.objects.get(code=data['account_type_code'])
        account, created = Account.objects.get_or_create(
            code=data['code'],
            defaults={
                'name': data['name'],
                'account_type': account_type,
                'opening_balance': data['opening_balance'],
                'current_balance': data['opening_balance'],
                'created_by': admin_user
            }
        )
        if created:
            print(f"Created account: {account.name}")
    
    # Create Payment Methods
    cash_account = Account.objects.get(code='1110')
    bank_account = Account.objects.get(code='1120')
    
    payment_methods = [
        {'name': 'Cash', 'payment_type': 'cash', 'account': cash_account},
        {'name': 'Bank Transfer', 'payment_type': 'bank_transfer', 'account': bank_account},
        {'name': 'Check', 'payment_type': 'check', 'account': bank_account},
    ]
    
    for data in payment_methods:
        payment_method, created = PaymentMethod.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"Created payment method: {payment_method.name}")
    
    # Create Accounting Period
    current_year = datetime.now().year
    start_date = datetime(current_year, 1, 1).date()
    end_date = datetime(current_year, 12, 31).date()
    
    period, created = AccountingPeriod.objects.get_or_create(
        name=f"Fiscal Year {current_year}",
        defaults={
            'start_date': start_date,
            'end_date': end_date,
            'is_current': True,
            'created_by': admin_user
        }
    )
    
    if created:
        print(f"Created accounting period: {period.name}")
    
    print("Financial initial data created successfully!")

if __name__ == '__main__':
    create_financial_data()
