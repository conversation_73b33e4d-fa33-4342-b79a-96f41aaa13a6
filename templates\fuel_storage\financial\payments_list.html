{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المدفوعات{% endblock %}

{% block extra_css %}
<style>
    .payment-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .payment-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .payment-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .payment-received::before {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .payment-paid::before {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .payment-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 20px;
    }
    
    .payment-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .payment-type-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .type-received {
        background: #d5f4e6;
        color: #27ae60;
    }
    
    .type-paid {
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .payment-amount {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .amount-received {
        color: #27ae60;
    }
    
    .amount-paid {
        color: #e74c3c;
    }
    
    .payment-method-badge {
        background: #f8f9fa;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .payment-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-top: 15px;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .detail-row:last-child {
        margin-bottom: 0;
    }
    
    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-value {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .summary-stats {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .stat-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .stat-received {
        background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
        color: #27ae60;
    }
    
    .stat-paid {
        background: linear-gradient(135deg, #fadbd8 0%, #f5b7b1 100%);
        color: #e74c3c;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .invoice-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }
    
    .invoice-link:hover {
        color: #764ba2;
        text-decoration: underline;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
        margin-top: 15px;
    }
    
    .btn-action {
        padding: 8px 12px;
        border-radius: 8px;
        border: none;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }
    
    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .btn-view:hover {
        background: #bbdefb;
        color: #0d47a1;
    }
    
    .btn-print {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .btn-print:hover {
        background: #e1bee7;
        color: #4a148c;
    }
    
    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .btn-edit:hover {
        background: #ffe0b2;
        color: #e65100;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        قائمة المدفوعات
                    </h1>
                    <p class="text-muted">إدارة ومتابعة جميع المدفوعات والمقبوضات</p>
                </div>
                <div>
                    <button class="btn btn-success me-2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </button>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-stats">
        <h5 class="mb-3">
            <i class="fas fa-chart-pie me-2"></i>
            ملخص المدفوعات
        </h5>
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card stat-received">
                    <div class="stat-value">{{ payments|length }}</div>
                    <div class="stat-label">إجمالي المقبوضات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-paid">
                    <div class="stat-value">{{ payments|length }}</div>
                    <div class="stat-label">إجمالي المدفوعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-received">
                    <div class="stat-value">0</div>
                    <div class="stat-label">صافي التدفق</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-value">{{ payments|length }}</div>
                    <div class="stat-label">إجمالي المعاملات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-2">
                <label for="payment_type" class="form-label">نوع الدفعة</label>
                <select name="payment_type" id="payment_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in payment_types %}
                        <option value="{{ value }}" {% if current_type_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="from_date" class="form-label">من تاريخ</label>
                <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date }}">
            </div>
            
            <div class="col-md-2">
                <label for="to_date" class="form-label">إلى تاريخ</label>
                <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date }}">
            </div>
            
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="رقم الدفعة، الفاتورة، العميل..." 
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Payments Grid -->
    <div class="row">
        {% for payment in payments %}
        <div class="col-lg-6 col-xl-4 mb-3">
            <div class="payment-card payment-{{ payment.payment_type }}">
                <div class="payment-header">
                    <div>
                        <div class="payment-number">{{ payment.payment_number }}</div>
                        <small class="text-muted">{{ payment.payment_date }}</small>
                    </div>
                    <div class="text-end">
                        <span class="payment-type-badge type-{{ payment.payment_type }}">
                            {{ payment.get_payment_type_display }}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="payment-amount amount-{{ payment.payment_type }}">
                        {% if payment.payment_type == 'received' %}+{% else %}-{% endif %}{{ payment.amount|floatformat:2 }}
                    </div>
                    <span class="payment-method-badge">
                        <i class="fas fa-credit-card me-1"></i>
                        {{ payment.payment_method.name }}
                    </span>
                </div>
                
                {% if payment.invoice %}
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-invoice me-2 text-muted"></i>
                        <a href="#" class="invoice-link">فاتورة {{ payment.invoice.invoice_number }}</a>
                    </div>
                </div>
                {% endif %}
                
                {% if payment.supplier %}
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-truck me-2 text-muted"></i>
                        <span class="fw-bold">{{ payment.supplier.full_name }}</span>
                    </div>
                </div>
                {% elif payment.beneficiary %}
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user me-2 text-muted"></i>
                        <span class="fw-bold">{{ payment.beneficiary.full_name }}</span>
                    </div>
                </div>
                {% endif %}
                
                {% if payment.notes %}
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-sticky-note me-1"></i>
                        {{ payment.notes|truncatewords:10 }}
                    </small>
                </div>
                {% endif %}
                
                <div class="payment-details">
                    {% if payment.reference_number %}
                    <div class="detail-row">
                        <span class="detail-label">رقم المرجع:</span>
                        <span class="detail-value">{{ payment.reference_number }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="detail-row">
                        <span class="detail-label">أنشئ بواسطة:</span>
                        <span class="detail-value">{{ payment.created_by.full_name }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">تاريخ الإنشاء:</span>
                        <span class="detail-value">{{ payment.created_at|date:"Y/m/d H:i" }}</span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn-action btn-view" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-action btn-print" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="btn-action btn-edit" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مدفوعات</h4>
                <p class="text-muted">لم يتم العثور على مدفوعات تطابق معايير البحث</p>
                <button class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول دفعة
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if payments %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="تصفح المدفوعات">
                <ul class="pagination justify-content-center">
                    <li class="page-item">
                        <a class="page-link" href="#" aria-label="السابق">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#" aria-label="التالي">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير hover للبطاقات
        const cards = document.querySelectorAll('.payment-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث العداد
        const paymentCount = {{ payments|length }};
        document.title = `قائمة المدفوعات (${paymentCount})`;
        
        // تأثيرات الأزرار
        const actionButtons = document.querySelectorAll('.btn-action');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // تأثير النقر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // هنا يمكن إضافة الوظائف الفعلية للأزرار
                const action = this.classList.contains('btn-view') ? 'view' :
                              this.classList.contains('btn-print') ? 'print' : 'edit';
                
                console.log(`تم النقر على زر ${action}`);
            });
        });
    });
</script>
{% endblock %}
