{% extends 'base.html' %}
{% load static %}
{% load financial_filters %}

{% block title %}قائمة الفواتير{% endblock %}

{% block extra_css %}
<style>
    .invoice-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .invoice-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .invoice-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .invoice-sales::before {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .invoice-purchase::before {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .invoice-return_sales::before {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }
    
    .invoice-return_purchase::before {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-draft {
        background: #f8f9fa;
        color: #6c757d;
    }
    
    .status-sent {
        background: #cce5ff;
        color: #0066cc;
    }
    
    .status-paid {
        background: #d4edda;
        color: #155724;
    }
    
    .status-partially_paid {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-overdue {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-cancelled {
        background: #e2e3e5;
        color: #383d41;
    }
    
    .invoice-amount {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 20px;
    }
    
    .invoice-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .invoice-type-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .type-sales {
        background: #d5f4e6;
        color: #27ae60;
    }
    
    .type-purchase {
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .type-return_sales {
        background: #fef9e7;
        color: #f39c12;
    }
    
    .type-return_purchase {
        background: #e8daef;
        color: #9b59b6;
    }
    
    .progress-payment {
        height: 8px;
        border-radius: 10px;
        background: #e9ecef;
        overflow: hidden;
    }
    
    .progress-payment .progress-bar {
        border-radius: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-file-invoice me-2"></i>
                        قائمة الفواتير
                    </h1>
                    <p class="text-muted">إدارة ومتابعة جميع الفواتير والمدفوعات</p>
                </div>
                <div>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-2">
                <label for="invoice_type" class="form-label">نوع الفاتورة</label>
                <select name="invoice_type" id="invoice_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in invoice_types %}
                        <option value="{{ value }}" {% if current_type_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if current_status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="from_date" class="form-label">من تاريخ</label>
                <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date }}">
            </div>
            
            <div class="col-md-2">
                <label for="to_date" class="form-label">إلى تاريخ</label>
                <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date }}">
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="رقم الفاتورة، العميل، المورد..." 
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Invoices Grid -->
    <div class="row">
        {% for invoice in invoices %}
        <div class="col-lg-6 col-xl-4 mb-3">
            <div class="invoice-card invoice-{{ invoice.invoice_type }}">
                <div class="invoice-header">
                    <div>
                        <div class="invoice-number">{{ invoice.invoice_number }}</div>
                        <small class="text-muted">{{ invoice.invoice_date }}</small>
                    </div>
                    <div class="text-end">
                        <span class="invoice-type-badge type-{{ invoice.invoice_type }}">
                            {{ invoice.get_invoice_type_display }}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    {% if invoice.supplier %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-truck me-2 text-muted"></i>
                            <span class="fw-bold">{{ invoice.supplier.full_name }}</span>
                        </div>
                    {% elif invoice.beneficiary %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-user me-2 text-muted"></i>
                            <span class="fw-bold">{{ invoice.beneficiary.full_name }}</span>
                        </div>
                    {% endif %}
                    
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar me-2 text-muted"></i>
                        <span class="text-muted">استحقاق: {{ invoice.due_date }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">المبلغ الإجمالي</span>
                        <span class="invoice-amount">{{ invoice.total_amount|floatformat:2 }}</span>
                    </div>
                    
                    {% if invoice.paid_amount > 0 %}
                    <div class="mb-2">
                        <div class="d-flex justify-content-between text-muted small">
                            <span>المدفوع: {{ invoice.paid_amount|floatformat:2 }}</span>
                            <span>المتبقي: {{ invoice.remaining_amount|floatformat:2 }}</span>
                        </div>
                        <div class="progress-payment mt-1">
                            <div class="progress-bar bg-success"
                                 style="width: {{ invoice.paid_amount|percentage:invoice.total_amount }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="status-badge status-{{ invoice.status }}">
                        {{ invoice.get_status_display }}
                    </span>
                    
                    <div class="btn-group" role="group">
                        <a href="{% url 'fuel_storage:invoice_detail' invoice.id %}"
                           class="btn btn-sm btn-outline-primary"
                           title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle"
                                    data-bs-toggle="dropdown" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item text-success" href="{% url 'fuel_storage:print_invoice_simple' invoice.id %}" target="_blank">
                                        <i class="fas fa-file-alt me-2"></i>
                                        <strong>طباعة HTML (موصى به)</strong>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item text-danger" href="{% url 'fuel_storage:print_invoice_pdf' invoice.id %}" target="_blank">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        <strong>طباعة PDF</strong>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        {% if invoice.status != 'paid' %}
                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                title="تسجيل دفعة">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-3 pt-3 border-top">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">المجموع الفرعي</small>
                            <small class="fw-bold">{{ invoice.subtotal|floatformat:2 }}</small>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">الضريبة</small>
                            <small class="fw-bold">{{ invoice.tax_amount|floatformat:2 }}</small>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">الخصم</small>
                            <small class="fw-bold">{{ invoice.discount_amount|floatformat:2 }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير</h4>
                <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Summary Section -->
    {% if invoices %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="filter-section">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملخص الفواتير
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-file-invoice fa-2x text-primary"></i>
                        </div>
                        <h6>إجمالي الفواتير</h6>
                        <p class="text-primary fw-bold">{{ invoices|length }}</p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                        <h6>مدفوعة</h6>
                        <p class="text-success fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                        <h6>معلقة</h6>
                        <p class="text-warning fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        </div>
                        <h6>متأخرة</h6>
                        <p class="text-danger fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير hover للبطاقات
        const cards = document.querySelectorAll('.invoice-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث العداد
        const invoiceCount = {{ invoices|length }};
        document.title = `قائمة الفواتير (${invoiceCount})`;
        
        // تحديث تلقائي كل 5 دقائق
        setInterval(function() {
            // يمكن إضافة تحديث تلقائي هنا
        }, 300000);
    });
</script>
{% endblock %}
