# نظام إدارة مخازن المحروقات

نظام شامل لإدارة مخازن المحروقات مبني بـ Django مع واجهات عربية كاملة.

## المميزات

### الوظائف الأساسية
- ✅ إدارة المخازن والأصناف
- ✅ إدارة الموردين والمستفيدين
- ✅ عمليات الوارد والصادر
- ✅ المرتجعات (وارد وصادر)
- ✅ عمليات التلف والتحويل المخزني
- ✅ تعديل العمليات مع تتبع التغييرات

### النظام المالي والمحاسبة 🆕
- ✅ إدارة الحسابات المحاسبية والأنواع
- ✅ قيود اليومية والمعاملات المالية
- ✅ الفواتير (مبيعات/مشتريات/مرتجعات)
- ✅ إدارة المدفوعات وطرق الدفع
- ✅ الفترات المحاسبية وإدارة الإقفال
- ✅ ربط تلقائي بين العمليات والنظام المالي
- ✅ التدفق النقدي والميزانيات

### التقارير المالية 🆕
- ✅ الميزانية العمومية
- ✅ قائمة الدخل (الأرباح والخسائر)
- ✅ تقرير التدفق النقدي
- ✅ تقارير الحسابات والأرصدة
- ✅ تقارير الفواتير والمدفوعات
- ✅ لوحة تحكم مالية شاملة

### التقارير المتقدمة
- ✅ تقرير حركة عامة للمخزن
- ✅ تقرير حالة المخزن
- ✅ تقرير حركة الصنف
- ✅ تقرير حالة الصنف
- ✅ تقرير حركة المستفيد
- ✅ تقرير حركة المورد/المحطة

### الحماية والأمان
- ✅ حماية من الحذف للعناصر المرتبطة
- ✅ التحقق من الكميات قبل العمليات
- ✅ قفل العمليات بعد الحفظ
- ✅ نظام أذونات متقدم

### التصدير
- ✅ تصدير PDF مع دعم العربية
- ✅ تصدير Excel مع تنسيق احترافي

## متطلبات النظام

- Python 3.8+
- Django 4.2+
- SQLite (افتراضي) أو PostgreSQL للإنتاج

## التثبيت السريع

### على Windows:
\`\`\`bash
# تشغيل ملف الإعداد التلقائي
setup.bat
\`\`\`

### على Linux/macOS:
\`\`\`bash
# إعطاء صلاحيات التنفيذ
chmod +x setup.sh

# تشغيل ملف الإعداد
./setup.sh
\`\`\`

### التثبيت اليدوي:

1. **إنشاء البيئة الافتراضية:**
\`\`\`bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
\`\`\`

2. **تثبيت المكتبات:**
\`\`\`bash
pip install -r requirements.txt
\`\`\`

3. **إنشاء قاعدة البيانات:**
\`\`\`bash
python manage.py makemigrations fuel_storage
python manage.py migrate
\`\`\`

4. **إنشاء مستخدم إداري:**
\`\`\`bash
python manage.py createsuperuser
\`\`\`

5. **تشغيل الخادم:**
\`\`\`bash
python manage.py runserver
\`\`\`

## الوصول للنظام

- **الواجهة الرئيسية:** http://127.0.0.1:8000/
- **لوحة الإدارة:** http://127.0.0.1:8000/admin/

### النظام المالي 🆕
- **لوحة التحكم المالية:** http://127.0.0.1:8000/financial/
- **إدارة الحسابات:** http://127.0.0.1:8000/financial/accounts/
- **إدارة الفواتير:** http://127.0.0.1:8000/financial/invoices/
- **إدارة المدفوعات:** http://127.0.0.1:8000/financial/payments/

### التقارير المالية 🆕
- **الميزانية العمومية:** http://127.0.0.1:8000/financial/reports/balance-sheet/
- **قائمة الدخل:** http://127.0.0.1:8000/financial/reports/income-statement/
- **التدفق النقدي:** http://127.0.0.1:8000/financial/reports/cash-flow/

## هيكل المشروع

\`\`\`
fuel_storage_system/
├── fuel_storage_system/          # إعدادات المشروع
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── fuel_storage/                 # التطبيق الرئيسي
│   ├── models.py                 # نماذج البيانات
│   ├── admin.py                  # واجهة الإدارة
│   ├── views.py                  # العروض
│   ├── urls.py                   # الروابط
│   └── utils.py                  # وظائف مساعدة
├── templates/                    # القوالب
│   ├── base.html
│   └── fuel_storage/
├── media/                        # الملفات المرفوعة
├── static/                       # الملفات الثابتة
├── logs/                         # ملفات السجلات
├── requirements.txt              # المكتبات المطلوبة
├── manage.py                     # أداة إدارة Django
├── setup.sh                     # إعداد تلقائي (Linux/macOS)
├── setup.bat                     # إعداد تلقائي (Windows)
└── README.md                     # هذا الملف
\`\`\`

## الاستخدام

### 1. إضافة البيانات الأساسية
- سجل دخول للوحة الإدارة
- أضف الأصناف والمخازن
- أضف الموردين والمستفيدين
- أضف المحطات

### 2. العمليات اليومية
- إنشاء عمليات الوارد
- إنشاء عمليات الصادر
- إدارة المرتجعات
- تسجيل عمليات التلف
- تحويل بين المخازن

### 3. التقارير
- اذهب إلى قسم التقارير المتقدمة
- اختر نوع التقرير المطلوب
- حدد المعايير والتواريخ
- صدر التقرير بصيغة PDF أو Excel

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

1. تحقق من ملفات السجلات في مجلد `logs/`
2. راجع رسائل الخطأ في terminal
3. تأكد من تثبيت جميع المكتبات المطلوبة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

---

**تم تطوير هذا النظام بواسطة v0 - نظام الذكي لتطوير التطبيقات**
