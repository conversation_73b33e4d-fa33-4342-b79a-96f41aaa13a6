from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Q
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
from django.contrib import messages
import json
from .models import (
    Storage, Category, Supplier, Beneficiary,
    StorageItem, IncomingOperation, OutgoingOperation,
    OperationModification, IncomingReturn, OutgoingReturn,
    DamageOperation, StorageTransfer, IncomingOperationItem,
    OutgoingOperationItem, DamageOperationItem, StorageTransferItem,
    # النماذج المالية
    Account, AccountType, Invoice, Payment, JournalEntry, Transaction,
    AccountingPeriod, CashFlow, Budget, BudgetItem, PaymentMethod,
    InvoiceItem
)
from .utils import generate_pdf_report, generate_excel_report

@login_required
def dashboard(request):
    # إحصائيات عامة
    total_storages = Storage.objects.filter(is_active=True).count()
    total_categories = Category.objects.filter(is_active=True).count()
    total_suppliers = Supplier.objects.filter(is_active=True).count()
    total_incoming_operations = IncomingOperation.objects.count()
    total_outgoing_operations = OutgoingOperation.objects.count()
    total_beneficiaries = Beneficiary.objects.filter(is_active=True).count()
    total_incoming_returns = IncomingReturn.objects.count()
    total_outgoing_returns = OutgoingReturn.objects.count()
    total_damage_operations = DamageOperation.objects.count()
    total_storage_transfers = StorageTransfer.objects.count()
    
    # أحدث العمليات
    recent_incoming = IncomingOperation.objects.select_related(
        'storage', 'supplier', 'station'
    ).order_by('-created_at')[:3]
    
    recent_outgoing = OutgoingOperation.objects.select_related(
        'storage', 'beneficiary'
    ).order_by('-created_at')[:3]
    
    # المخازن مع أصنافها
    storages_with_items = Storage.objects.filter(is_active=True).prefetch_related('storageitem_set__category')[:5]
    
    context = {
        'total_storages': total_storages,
        'total_categories': total_categories,
        'total_suppliers': total_suppliers,
        'total_incoming_operations': total_incoming_operations,
        'total_outgoing_operations': total_outgoing_operations,
        'total_beneficiaries': total_beneficiaries,
        'total_incoming_returns': total_incoming_returns,
        'total_outgoing_returns': total_outgoing_returns,
        'total_damage_operations': total_damage_operations,
        'total_storage_transfers': total_storage_transfers,
        'recent_incoming': recent_incoming,
        'recent_outgoing': recent_outgoing,
        'storages_with_items': storages_with_items,
    }
    
    return render(request, 'fuel_storage/dashboard.html', context)

@login_required
def storage_list(request):
    storages = Storage.objects.filter(is_active=True).order_by('name')
    return render(request, 'fuel_storage/storage_list.html', {'storages': storages})

@login_required
def category_list(request):
    categories = Category.objects.filter(is_active=True).order_by('name')
    return render(request, 'fuel_storage/category_list.html', {'categories': categories})

@login_required
def supplier_list(request):
    suppliers = Supplier.objects.filter(is_active=True).order_by('full_name')
    return render(request, 'fuel_storage/supplier_list.html', {'suppliers': suppliers})

@login_required
def incoming_operations_list(request):
    operations = IncomingOperation.objects.select_related(
        'storage', 'supplier', 'station', 'created_by'
    ).order_by('-operation_date')
    return render(request, 'fuel_storage/incoming_operations_list.html', {'operations': operations})

@login_required
def storage_items_list(request):
    items = StorageItem.objects.select_related(
        'storage', 'category'
    ).filter(storage__is_active=True, category__is_active=True).order_by('storage__name', 'category__name')
    return render(request, 'fuel_storage/storage_items_list.html', {'items': items})

@login_required
def outgoing_operations_list(request):
    operations = OutgoingOperation.objects.select_related(
        'storage', 'beneficiary', 'created_by'
    ).order_by('-operation_date')
    return render(request, 'fuel_storage/outgoing_operations_list.html', {'operations': operations})

@login_required
def beneficiary_list(request):
    beneficiaries = Beneficiary.objects.filter(is_active=True).order_by('full_name')
    return render(request, 'fuel_storage/beneficiary_list.html', {'beneficiaries': beneficiaries})

@login_required
def operation_modifications_list(request):
    modifications = OperationModification.objects.select_related(
        'storage', 'category', 'modified_by'
    ).order_by('-modification_date')
    return render(request, 'fuel_storage/operation_modifications_list.html', {'modifications': modifications})

@login_required
def storage_report(request):
    storage_id = request.GET.get('storage_id')
    storages = Storage.objects.filter(is_active=True)
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        storage_items = StorageItem.objects.filter(
            storage=selected_storage, 
            category__is_active=True
        ).select_related('category')
        
        # إحصائيات العمليات
        incoming_count = IncomingOperation.objects.filter(storage=selected_storage).count()
        outgoing_count = OutgoingOperation.objects.filter(storage=selected_storage).count()
        
        context = {
            'storages': storages,
            'selected_storage': selected_storage,
            'storage_items': storage_items,
            'incoming_count': incoming_count,
            'outgoing_count': outgoing_count,
        }
    else:
        context = {'storages': storages}
    
    return render(request, 'fuel_storage/storage_report.html', context)

@login_required
def incoming_returns_list(request):
    returns = IncomingReturn.objects.select_related(
        'incoming_operation', 'incoming_operation__storage', 
        'incoming_operation__supplier', 'created_by'
    ).order_by('-return_date')
    return render(request, 'fuel_storage/incoming_returns_list.html', {'returns': returns})

@login_required
def outgoing_returns_list(request):
    returns = OutgoingReturn.objects.select_related(
        'outgoing_operation', 'outgoing_operation__storage', 
        'outgoing_operation__beneficiary', 'created_by'
    ).order_by('-return_date')
    return render(request, 'fuel_storage/outgoing_returns_list.html', {'returns': returns})

@login_required
def returns_summary(request):
    # إحصائيات المرتجعات
    total_incoming_returns = IncomingReturn.objects.count()
    total_outgoing_returns = OutgoingReturn.objects.count()
    
    # أحدث المرتجعات
    recent_incoming_returns = IncomingReturn.objects.select_related(
        'incoming_operation', 'created_by'
    ).order_by('-created_at')[:5]
    
    recent_outgoing_returns = OutgoingReturn.objects.select_related(
        'outgoing_operation', 'created_by'
    ).order_by('-created_at')[:5]
    
    context = {
        'total_incoming_returns': total_incoming_returns,
        'total_outgoing_returns': total_outgoing_returns,
        'recent_incoming_returns': recent_incoming_returns,
        'recent_outgoing_returns': recent_outgoing_returns,
    }
    
    return render(request, 'fuel_storage/returns_summary.html', context)

@login_required
def damage_operations_list(request):
    operations = DamageOperation.objects.select_related(
        'storage', 'created_by'
    ).order_by('-damage_date')
    return render(request, 'fuel_storage/damage_operations_list.html', {'operations': operations})

@login_required
def storage_transfers_list(request):
    transfers = StorageTransfer.objects.select_related(
        'from_storage', 'to_storage', 'created_by'
    ).order_by('-transfer_date')
    return render(request, 'fuel_storage/storage_transfers_list.html', {'transfers': transfers})

@login_required
def operations_summary(request):
    # إحصائيات العمليات
    total_damage_operations = DamageOperation.objects.count()
    total_storage_transfers = StorageTransfer.objects.count()
    
    # أحدث العمليات
    recent_damage_operations = DamageOperation.objects.select_related(
        'storage', 'created_by'
    ).order_by('-created_at')[:5]
    
    recent_storage_transfers = StorageTransfer.objects.select_related(
        'from_storage', 'to_storage', 'created_by'
    ).order_by('-created_at')[:5]
    
    context = {
        'total_damage_operations': total_damage_operations,
        'total_storage_transfers': total_storage_transfers,
        'recent_damage_operations': recent_damage_operations,
        'recent_storage_transfers': recent_storage_transfers,
    }
    
    return render(request, 'fuel_storage/operations_summary.html', context)

# تقارير متقدمة
@login_required
def storage_movement_report(request):
    """تقرير حركة عامة للمخزن"""
    storage_id = request.GET.get('storage_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    export_format = request.GET.get('export')
    
    storages = Storage.objects.filter(is_active=True)
    context = {'storages': storages}
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        
        # تطبيق فلتر التاريخ
        date_filter = Q()
        if from_date:
            date_filter &= Q(operation_date__gte=from_date)
        if to_date:
            date_filter &= Q(operation_date__lte=to_date)
        
        # عمليات الوارد
        incoming_operations = IncomingOperation.objects.filter(
            storage=selected_storage
        ).filter(date_filter).select_related('supplier', 'station').prefetch_related('items__category')
        
        # عمليات الصادر
        outgoing_operations = OutgoingOperation.objects.filter(
            storage=selected_storage
        ).filter(date_filter).select_related('beneficiary').prefetch_related('items__category')
        
        # عمليات المرتجع
        incoming_returns = IncomingReturn.objects.filter(
            incoming_operation__storage=selected_storage
        ).filter(return_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                return_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        outgoing_returns = OutgoingReturn.objects.filter(
            outgoing_operation__storage=selected_storage
        ).filter(return_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                return_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        # عمليات التلف
        damage_operations = DamageOperation.objects.filter(
            storage=selected_storage
        ).filter(damage_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                damage_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        # عمليات التحويل
        transfer_from = StorageTransfer.objects.filter(
            from_storage=selected_storage
        ).filter(transfer_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                transfer_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        transfer_to = StorageTransfer.objects.filter(
            to_storage=selected_storage
        ).filter(transfer_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                transfer_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        context.update({
            'selected_storage': selected_storage,
            'incoming_operations': incoming_operations,
            'outgoing_operations': outgoing_operations,
            'incoming_returns': incoming_returns,
            'outgoing_returns': outgoing_returns,
            'damage_operations': damage_operations,
            'transfer_from': transfer_from,
            'transfer_to': transfer_to,
            'from_date': from_date,
            'to_date': to_date,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حركة المخزن - {selected_storage.name}',
                'storage': selected_storage,
                'data': context,
                'from_date': from_date,
                'to_date': to_date,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'storage_movement')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'storage_movement')
    
    return render(request, 'fuel_storage/reports/storage_movement_report.html', context)

@login_required
def storage_status_report(request):
    """تقرير حالة مخزن"""
    storage_id = request.GET.get('storage_id')
    export_format = request.GET.get('export')
    
    storages = Storage.objects.filter(is_active=True)
    context = {'storages': storages}
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        storage_items = StorageItem.objects.filter(
            storage=selected_storage,
            category__is_active=True
        ).select_related('category').order_by('category__name')
        
        context.update({
            'selected_storage': selected_storage,
            'storage_items': storage_items,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حالة المخزن - {selected_storage.name}',
                'storage': selected_storage,
                'items': storage_items,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'storage_status')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'storage_status')
    
    return render(request, 'fuel_storage/reports/storage_status_report.html', context)

@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    return render(request, 'fuel_storage/reports/reports_dashboard.html')


# ================================
# العروض المالية والمحاسبية
# ================================

@login_required
def financial_dashboard(request):
    """لوحة تحكم النظام المالي"""
    # إحصائيات مالية عامة
    total_accounts = Account.objects.filter(is_active=True).count()
    total_invoices = Invoice.objects.count()
    total_payments = Payment.objects.count()
    current_period = AccountingPeriod.objects.filter(is_current=True).first()

    # إجمالي الأرصدة
    asset_accounts = Account.objects.filter(account_type__account_type='asset', is_active=True)
    total_assets = sum(account.get_balance() for account in asset_accounts)

    liability_accounts = Account.objects.filter(account_type__account_type='liability', is_active=True)
    total_liabilities = sum(account.get_balance() for account in liability_accounts)

    equity_accounts = Account.objects.filter(account_type__account_type='equity', is_active=True)
    total_equity = sum(account.get_balance() for account in equity_accounts)

    # الفواتير المستحقة
    overdue_invoices = Invoice.objects.filter(
        status__in=['sent', 'partially_paid'],
        due_date__lt=timezone.now().date()
    ).count()

    # إجمالي المبيعات والمشتريات هذا الشهر
    current_month = timezone.now().replace(day=1).date()
    monthly_sales = Invoice.objects.filter(
        invoice_type='sales',
        invoice_date__gte=current_month
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    monthly_purchases = Invoice.objects.filter(
        invoice_type='purchase',
        invoice_date__gte=current_month
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # أحدث المعاملات
    recent_transactions = Transaction.objects.select_related(
        'journal_entry', 'account'
    ).order_by('-transaction_date')[:10]

    # أحدث الفواتير
    recent_invoices = Invoice.objects.select_related(
        'supplier', 'beneficiary'
    ).order_by('-created_at')[:5]

    context = {
        'total_accounts': total_accounts,
        'total_invoices': total_invoices,
        'total_payments': total_payments,
        'current_period': current_period,
        'total_assets': total_assets,
        'total_liabilities': total_liabilities,
        'total_equity': total_equity,
        'overdue_invoices': overdue_invoices,
        'monthly_sales': monthly_sales,
        'monthly_purchases': monthly_purchases,
        'recent_transactions': recent_transactions,
        'recent_invoices': recent_invoices,
    }

    return render(request, 'fuel_storage/financial/dashboard.html', context)

@login_required
def accounts_list(request):
    """قائمة الحسابات"""
    accounts = Account.objects.filter(is_active=True).select_related('account_type').order_by('code')

    # تصفية حسب نوع الحساب
    account_type_filter = request.GET.get('account_type')
    if account_type_filter:
        accounts = accounts.filter(account_type__account_type=account_type_filter)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        accounts = accounts.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # أنواع الحسابات للتصفية
    account_types = AccountType.ACCOUNT_TYPES

    context = {
        'accounts': accounts,
        'account_types': account_types,
        'current_filter': account_type_filter,
        'search_query': search_query,
    }

    return render(request, 'fuel_storage/financial/accounts_list.html', context)

@login_required
def invoices_list(request):
    """قائمة الفواتير"""
    invoices = Invoice.objects.select_related('supplier', 'beneficiary').order_by('-invoice_date')

    # تصفية حسب نوع الفاتورة
    invoice_type_filter = request.GET.get('invoice_type')
    if invoice_type_filter:
        invoices = invoices.filter(invoice_type=invoice_type_filter)

    # تصفية حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        invoices = invoices.filter(status=status_filter)

    # تصفية حسب التاريخ
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    if from_date:
        invoices = invoices.filter(invoice_date__gte=from_date)
    if to_date:
        invoices = invoices.filter(invoice_date__lte=to_date)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search_query) |
            Q(supplier__full_name__icontains=search_query) |
            Q(beneficiary__full_name__icontains=search_query)
        )

    context = {
        'invoices': invoices,
        'invoice_types': Invoice.INVOICE_TYPES,
        'status_choices': Invoice.STATUS_CHOICES,
        'current_type_filter': invoice_type_filter,
        'current_status_filter': status_filter,
        'from_date': from_date,
        'to_date': to_date,
        'search_query': search_query,
    }

    return render(request, 'fuel_storage/financial/invoices_list.html', context)

@login_required
def payments_list(request):
    """قائمة المدفوعات"""
    payments = Payment.objects.select_related(
        'payment_method', 'invoice', 'supplier', 'beneficiary'
    ).order_by('-payment_date')

    # تصفية حسب نوع الدفعة
    payment_type_filter = request.GET.get('payment_type')
    if payment_type_filter:
        payments = payments.filter(payment_type=payment_type_filter)

    # تصفية حسب التاريخ
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    if from_date:
        payments = payments.filter(payment_date__gte=from_date)
    if to_date:
        payments = payments.filter(payment_date__lte=to_date)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        payments = payments.filter(
            Q(payment_number__icontains=search_query) |
            Q(invoice__invoice_number__icontains=search_query) |
            Q(supplier__full_name__icontains=search_query) |
            Q(beneficiary__full_name__icontains=search_query)
        )

    context = {
        'payments': payments,
        'payment_types': Payment.PAYMENT_TYPES,
        'current_type_filter': payment_type_filter,
        'from_date': from_date,
        'to_date': to_date,
        'search_query': search_query,
    }

    return render(request, 'fuel_storage/financial/payments_list.html', context)

@login_required
def balance_sheet_report(request):
    """تقرير الميزانية العمومية"""
    report_date = request.GET.get('date', timezone.now().date())
    if isinstance(report_date, str):
        report_date = datetime.strptime(report_date, '%Y-%m-%d').date()

    # الأصول
    asset_accounts = Account.objects.filter(
        account_type__account_type='asset',
        is_active=True
    ).order_by('code')

    assets = []
    total_assets = 0
    for account in asset_accounts:
        balance = account.get_balance(report_date)
        if balance != 0:
            assets.append({
                'account': account,
                'balance': balance
            })
            total_assets += balance

    # الخصوم
    liability_accounts = Account.objects.filter(
        account_type__account_type='liability',
        is_active=True
    ).order_by('code')

    liabilities = []
    total_liabilities = 0
    for account in liability_accounts:
        balance = account.get_balance(report_date)
        if balance != 0:
            liabilities.append({
                'account': account,
                'balance': balance
            })
            total_liabilities += balance

    # حقوق الملكية
    equity_accounts = Account.objects.filter(
        account_type__account_type='equity',
        is_active=True
    ).order_by('code')

    equity = []
    total_equity = 0
    for account in equity_accounts:
        balance = account.get_balance(report_date)
        if balance != 0:
            equity.append({
                'account': account,
                'balance': balance
            })
            total_equity += balance

    context = {
        'report_date': report_date,
        'assets': assets,
        'liabilities': liabilities,
        'equity': equity,
        'total_assets': total_assets,
        'total_liabilities': total_liabilities,
        'total_equity': total_equity,
        'total_liabilities_equity': total_liabilities + total_equity,
    }

    return render(request, 'fuel_storage/financial/balance_sheet.html', context)

@login_required
def income_statement_report(request):
    """تقرير الأرباح والخسائر"""
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')

    if not from_date or not to_date:
        # افتراضي: الشهر الحالي
        today = timezone.now().date()
        from_date = today.replace(day=1)
        to_date = today
    else:
        from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date, '%Y-%m-%d').date()

    # الإيرادات
    revenue_accounts = Account.objects.filter(
        account_type__account_type='revenue',
        is_active=True
    ).order_by('code')

    revenues = []
    total_revenue = 0
    for account in revenue_accounts:
        transactions = Transaction.objects.filter(
            account=account,
            transaction_date__gte=from_date,
            transaction_date__lte=to_date
        )
        balance = transactions.aggregate(total=Sum('credit_amount'))['total'] or 0
        balance -= transactions.aggregate(total=Sum('debit_amount'))['total'] or 0

        if balance != 0:
            revenues.append({
                'account': account,
                'balance': balance
            })
            total_revenue += balance

    # المصروفات
    expense_accounts = Account.objects.filter(
        account_type__account_type='expense',
        is_active=True
    ).order_by('code')

    expenses = []
    total_expenses = 0
    for account in expense_accounts:
        transactions = Transaction.objects.filter(
            account=account,
            transaction_date__gte=from_date,
            transaction_date__lte=to_date
        )
        balance = transactions.aggregate(total=Sum('debit_amount'))['total'] or 0
        balance -= transactions.aggregate(total=Sum('credit_amount'))['total'] or 0

        if balance != 0:
            expenses.append({
                'account': account,
                'balance': balance
            })
            total_expenses += balance

    # تكلفة البضاعة المباعة
    cogs_accounts = Account.objects.filter(
        account_type__account_type='cost_of_goods',
        is_active=True
    ).order_by('code')

    cogs = []
    total_cogs = 0
    for account in cogs_accounts:
        transactions = Transaction.objects.filter(
            account=account,
            transaction_date__gte=from_date,
            transaction_date__lte=to_date
        )
        balance = transactions.aggregate(total=Sum('debit_amount'))['total'] or 0
        balance -= transactions.aggregate(total=Sum('credit_amount'))['total'] or 0

        if balance != 0:
            cogs.append({
                'account': account,
                'balance': balance
            })
            total_cogs += balance

    # حساب الأرباح
    gross_profit = total_revenue - total_cogs
    net_profit = gross_profit - total_expenses

    context = {
        'from_date': from_date,
        'to_date': to_date,
        'revenues': revenues,
        'expenses': expenses,
        'cogs': cogs,
        'total_revenue': total_revenue,
        'total_expenses': total_expenses,
        'total_cogs': total_cogs,
        'gross_profit': gross_profit,
        'net_profit': net_profit,
    }

    return render(request, 'fuel_storage/financial/income_statement.html', context)

@login_required
def cash_flow_report(request):
    """تقرير التدفق النقدي"""
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')

    if not from_date or not to_date:
        # افتراضي: الشهر الحالي
        today = timezone.now().date()
        from_date = today.replace(day=1)
        to_date = today
    else:
        from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date, '%Y-%m-%d').date()

    # التدفقات النقدية
    cash_flows = CashFlow.objects.filter(
        date__gte=from_date,
        date__lte=to_date
    ).order_by('date')

    # تجميع حسب الفئة
    operating_inflows = cash_flows.filter(category='operating', flow_type='inflow')
    operating_outflows = cash_flows.filter(category='operating', flow_type='outflow')
    investing_inflows = cash_flows.filter(category='investing', flow_type='inflow')
    investing_outflows = cash_flows.filter(category='investing', flow_type='outflow')
    financing_inflows = cash_flows.filter(category='financing', flow_type='inflow')
    financing_outflows = cash_flows.filter(category='financing', flow_type='outflow')

    # حساب الإجماليات
    total_operating_inflows = operating_inflows.aggregate(total=Sum('amount'))['total'] or 0
    total_operating_outflows = operating_outflows.aggregate(total=Sum('amount'))['total'] or 0
    net_operating = total_operating_inflows - total_operating_outflows

    total_investing_inflows = investing_inflows.aggregate(total=Sum('amount'))['total'] or 0
    total_investing_outflows = investing_outflows.aggregate(total=Sum('amount'))['total'] or 0
    net_investing = total_investing_inflows - total_investing_outflows

    total_financing_inflows = financing_inflows.aggregate(total=Sum('amount'))['total'] or 0
    total_financing_outflows = financing_outflows.aggregate(total=Sum('amount'))['total'] or 0
    net_financing = total_financing_inflows - total_financing_outflows

    net_cash_flow = net_operating + net_investing + net_financing

    context = {
        'from_date': from_date,
        'to_date': to_date,
        'operating_inflows': operating_inflows,
        'operating_outflows': operating_outflows,
        'investing_inflows': investing_inflows,
        'investing_outflows': investing_outflows,
        'financing_inflows': financing_inflows,
        'financing_outflows': financing_outflows,
        'total_operating_inflows': total_operating_inflows,
        'total_operating_outflows': total_operating_outflows,
        'net_operating': net_operating,
        'total_investing_inflows': total_investing_inflows,
        'total_investing_outflows': total_investing_outflows,
        'net_investing': net_investing,
        'total_financing_inflows': total_financing_inflows,
        'total_financing_outflows': total_financing_outflows,
        'net_financing': net_financing,
        'net_cash_flow': net_cash_flow,
    }

    return render(request, 'fuel_storage/financial/cash_flow.html', context)

@login_required
def add_payment(request):
    """إضافة دفعة جديدة"""
    if request.method == 'POST':
        try:
            # الحصول على البيانات من النموذج
            payment_type = request.POST.get('payment_type')
            amount = request.POST.get('amount')
            payment_method_id = request.POST.get('payment_method')
            payment_date = request.POST.get('payment_date')
            invoice_id = request.POST.get('invoice')
            supplier_id = request.POST.get('supplier')
            beneficiary_id = request.POST.get('beneficiary')
            reference_number = request.POST.get('reference_number')
            notes = request.POST.get('notes')

            # التحقق من صحة البيانات
            if not all([payment_type, amount, payment_method_id, payment_date]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect('fuel_storage:add_payment')

            # إنشاء رقم دفعة تلقائي
            payment_count = Payment.objects.count() + 1
            payment_number = f"PAY-{payment_count:06d}"

            # إنشاء الدفعة
            payment_data = {
                'payment_number': payment_number,
                'payment_type': payment_type,
                'amount': amount,
                'payment_method_id': payment_method_id,
                'payment_date': payment_date,
                'reference_number': reference_number,
                'notes': notes,
                'created_by': request.user
            }

            # إضافة المرجع حسب النوع
            if invoice_id:
                payment_data['invoice_id'] = invoice_id
            elif supplier_id:
                payment_data['supplier_id'] = supplier_id
            elif beneficiary_id:
                payment_data['beneficiary_id'] = beneficiary_id

            payment = Payment.objects.create(**payment_data)

            # إنشاء القيد المحاسبي
            payment.create_journal_entry(request.user)

            messages.success(request, f'تم إنشاء الدفعة {payment.payment_number} بنجاح')
            return redirect('fuel_storage:payments_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدفعة: {str(e)}')

    # الحصول على البيانات للنموذج
    payment_methods = PaymentMethod.objects.filter(is_active=True)
    invoices = Invoice.objects.filter(status__in=['sent', 'partially_paid']).order_by('-invoice_date')[:50]
    suppliers = Supplier.objects.filter(is_active=True).order_by('full_name')
    beneficiaries = Beneficiary.objects.filter(is_active=True).order_by('full_name')

    context = {
        'payment_methods': payment_methods,
        'invoices': invoices,
        'suppliers': suppliers,
        'beneficiaries': beneficiaries,
        'payment_types': Payment.PAYMENT_TYPES,
    }

    return render(request, 'fuel_storage/financial/add_payment.html', context)

@login_required
def payment_detail(request, payment_id):
    """عرض تفاصيل الدفعة"""
    payment = get_object_or_404(Payment, id=payment_id)

    context = {
        'payment': payment,
    }

    return render(request, 'fuel_storage/financial/payment_detail.html', context)

@login_required
def invoice_detail(request, invoice_id):
    """عرض تفاصيل الفاتورة"""
    invoice = get_object_or_404(Invoice, id=invoice_id)

    context = {
        'invoice': invoice,
        'items': invoice.items.all(),
    }

    return render(request, 'fuel_storage/financial/invoice_detail.html', context)

@login_required
def print_invoice_pdf(request, invoice_id):
    """إعادة توجيه إلى طباعة HTML للحصول على أفضل دعم للعربية"""
    from django.shortcuts import redirect
    from django.urls import reverse

    # إعادة توجيه إلى print-simple للحصول على أفضل دعم للعربية
    return redirect('fuel_storage:print_invoice_simple', invoice_id=invoice_id)



@login_required
def print_invoice_simple(request, invoice_id):
    """طباعة الفاتورة بتنسيق HTML بسيط للطباعة"""
    invoice = get_object_or_404(Invoice, id=invoice_id)

    context = {
        'invoice': invoice,
        'items': invoice.items.all(),
        'company_name': 'شركة إدارة مخازن المحروقات',
        'company_address': 'المملكة العربية السعودية',
        'company_phone': '+966 XX XXX XXXX',
        'company_email': '<EMAIL>',
    }

    return render(request, 'fuel_storage/financial/invoice_print_simple.html', context)

@login_required
def test_links(request):
    """صفحة اختبار روابط الطباعة"""
    return render(request, 'fuel_storage/test_links.html')
