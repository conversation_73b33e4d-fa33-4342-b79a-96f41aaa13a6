from django.urls import path
from . import views

app_name = 'fuel_storage'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('storages/', views.storage_list, name='storage_list'),
    path('categories/', views.category_list, name='category_list'),
    path('suppliers/', views.supplier_list, name='supplier_list'),
    path('beneficiaries/', views.beneficiary_list, name='beneficiary_list'),
    path('incoming-operations/', views.incoming_operations_list, name='incoming_operations_list'),
    path('outgoing-operations/', views.outgoing_operations_list, name='outgoing_operations_list'),
    path('operation-modifications/', views.operation_modifications_list, name='operation_modifications_list'),
    path('storage-items/', views.storage_items_list, name='storage_items_list'),
    path('storage-report/', views.storage_report, name='storage_report'),
    path('incoming-returns/', views.incoming_returns_list, name='incoming_returns_list'),
    path('outgoing-returns/', views.outgoing_returns_list, name='outgoing_returns_list'),
    path('returns-summary/', views.returns_summary, name='returns_summary'),
    path('damage-operations/', views.damage_operations_list, name='damage_operations_list'),
    path('storage-transfers/', views.storage_transfers_list, name='storage_transfers_list'),
    path('operations-summary/', views.operations_summary, name='operations_summary'),
    
    # التقارير المتقدمة
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/storage-movement/', views.storage_movement_report, name='storage_movement_report'),
    path('reports/storage-status/', views.storage_status_report, name='storage_status_report'),

    # النظام المالي والمحاسبة
    path('financial/', views.financial_dashboard, name='financial_dashboard'),
    path('financial/accounts/', views.accounts_list, name='accounts_list'),
    path('financial/invoices/', views.invoices_list, name='invoices_list'),
    path('financial/payments/', views.payments_list, name='payments_list'),
    path('financial/payments/add/', views.add_payment, name='add_payment'),
    path('financial/payments/<int:payment_id>/', views.payment_detail, name='payment_detail'),
    path('financial/invoices/<int:invoice_id>/', views.invoice_detail, name='invoice_detail'),
    path('financial/invoices/<int:invoice_id>/print/', views.print_invoice_pdf, name='print_invoice_pdf'),
    path('financial/invoices/<int:invoice_id>/print-simple/', views.print_invoice_simple, name='print_invoice_simple'),

    # التقارير المالية
    path('financial/reports/balance-sheet/', views.balance_sheet_report, name='balance_sheet_report'),
    path('financial/reports/income-statement/', views.income_statement_report, name='income_statement_report'),
    path('financial/reports/cash-flow/', views.cash_flow_report, name='cash_flow_report'),
]
