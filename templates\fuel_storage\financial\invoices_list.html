{% extends 'base.html' %}
{% load static %}
{% load financial_filters %}

{% block title %}قائمة الفواتير{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: 0 0 20px 20px;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .filters-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .filters-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .filters-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .invoice-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: none;
    }

    .invoice-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: var(--invoice-color);
    }

    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .invoice-sales {
        --invoice-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .invoice-purchase {
        --invoice-color: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .invoice-return_sales {
        --invoice-color: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }

    .invoice-return_purchase {
        --invoice-color: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    .invoice-header {
        padding: 1.5rem;
        border-bottom: 1px solid #f1f3f4;
        background: linear-gradient(45deg, #fafbfc, #f8f9fa);
    }

    .invoice-number {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .invoice-type {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .invoice-body {
        padding: 1.5rem;
    }

    .invoice-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.2rem;
    }

    .info-value {
        font-weight: 600;
        color: #2c3e50;
    }

    .invoice-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        padding-top: 1rem;
        border-top: 1px solid #f1f3f4;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .btn-print {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .btn-view {
        background: linear-gradient(135deg, #007bff, #6610f2);
        color: white;
    }

    .btn-edit {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-paid {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        color: #155724;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        color: #856404;
    }

    .status-overdue {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        color: #721c24;
    }

    .amount-display {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        margin-top: 1rem;
    }

    .no-invoices {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-invoices i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .page-header {
            margin: -1rem -1rem 1.5rem -1rem;
            padding: 1.5rem 0;
        }

        .page-title {
            font-size: 2rem;
        }

        .invoice-info {
            grid-template-columns: 1fr;
        }

        .invoice-actions {
            flex-direction: column;
        }

        .btn-action {
            justify-content: center;
        }
    }
</style>
{% endblock %}
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-draft {
        background: #f8f9fa;
        color: #6c757d;
    }
    
    .status-sent {
        background: #cce5ff;
        color: #0066cc;
    }
    
    .status-paid {
        background: #d4edda;
        color: #155724;
    }
    
    .status-partially_paid {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-overdue {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-cancelled {
        background: #e2e3e5;
        color: #383d41;
    }
    
    .invoice-amount {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 20px;
    }
    
    .invoice-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .invoice-type-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .type-sales {
        background: #d5f4e6;
        color: #27ae60;
    }
    
    .type-purchase {
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .type-return_sales {
        background: #fef9e7;
        color: #f39c12;
    }
    
    .type-return_purchase {
        background: #e8daef;
        color: #9b59b6;
    }
    
    .progress-payment {
        height: 8px;
        border-radius: 10px;
        background: #e9ecef;
        overflow: hidden;
    }
    
    .progress-payment .progress-bar {
        border-radius: 10px;
    }
</style>
{% endblock extra_css %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-file-invoice me-3"></i>
            قائمة الفواتير
        </h1>
        <p class="page-subtitle">إدارة ومتابعة جميع الفواتير والمدفوعات بطريقة احترافية</p>
    </div>
</div>

<div class="container-fluid">
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card filters-card">
        <div class="filters-header">
            <h5 class="filters-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="invoice_type" class="form-label">
                        <i class="fas fa-file-alt me-1"></i>
                        نوع الفاتورة
                    </label>
                    <select name="invoice_type" id="invoice_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in invoice_types %}
                            <option value="{{ value }}" {% if current_type_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">
                        <i class="fas fa-info-circle me-1"></i>
                        الحالة
                    </label>
                    <select name="status" id="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if current_status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="from_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        من تاريخ
                    </label>
                    <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date }}">
                </div>

                <div class="col-md-2">
                    <label for="to_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        إلى تاريخ
                    </label>
                    <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date }}">
                </div>

                <div class="col-md-3">
                    <label for="search" class="form-label">
                        <i class="fas fa-search me-1"></i>
                        البحث
                    </label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="رقم الفاتورة، العميل، المورد..."
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Invoices Grid -->
    <div class="row">
        {% for invoice in invoices %}
        <div class="col-lg-6 col-xl-4 mb-3">
            <div class="invoice-card invoice-{{ invoice.invoice_type }}">
                <div class="invoice-header">
                    <div>
                        <div class="invoice-number">{{ invoice.invoice_number }}</div>
                        <small class="text-muted">{{ invoice.invoice_date }}</small>
                    </div>
                    <div class="text-end">
                        <span class="invoice-type-badge type-{{ invoice.invoice_type }}">
                            {{ invoice.get_invoice_type_display }}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    {% if invoice.supplier %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-truck me-2 text-muted"></i>
                            <span class="fw-bold">{{ invoice.supplier.full_name }}</span>
                        </div>
                    {% elif invoice.beneficiary %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-user me-2 text-muted"></i>
                            <span class="fw-bold">{{ invoice.beneficiary.full_name }}</span>
                        </div>
                    {% endif %}
                    
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar me-2 text-muted"></i>
                        <span class="text-muted">استحقاق: {{ invoice.due_date }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">المبلغ الإجمالي</span>
                        <span class="invoice-amount">{{ invoice.total_amount|floatformat:2 }}</span>
                    </div>
                    
                    {% if invoice.paid_amount > 0 %}
                    <div class="mb-2">
                        <div class="d-flex justify-content-between text-muted small">
                            <span>المدفوع: {{ invoice.paid_amount|floatformat:2 }}</span>
                            <span>المتبقي: {{ invoice.remaining_amount|floatformat:2 }}</span>
                        </div>
                        <div class="progress-payment mt-1">
                            <div class="progress-bar bg-success"
                                 style="width: {{ invoice.paid_amount|percentage:invoice.total_amount }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="status-badge status-{{ invoice.status }}">
                        {{ invoice.get_status_display }}
                    </span>
                    
                    <div class="btn-group" role="group">
                        <a href="{% url 'fuel_storage:invoice_detail' invoice.id %}"
                           class="btn btn-sm btn-outline-primary"
                           title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'fuel_storage:print_invoice_simple' invoice.id %}" target="_blank"
                           class="btn btn-sm btn-outline-success" title="طباعة الفاتورة">
                            <i class="fas fa-print"></i>
                        </a>
                        {% if invoice.status != 'paid' %}
                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                title="تسجيل دفعة">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-3 pt-3 border-top">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">المجموع الفرعي</small>
                            <small class="fw-bold">{{ invoice.subtotal|floatformat:2 }}</small>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">الضريبة</small>
                            <small class="fw-bold">{{ invoice.tax_amount|floatformat:2 }}</small>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">الخصم</small>
                            <small class="fw-bold">{{ invoice.discount_amount|floatformat:2 }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير</h4>
                <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Summary Section -->
    {% if invoices %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="filter-section">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملخص الفواتير
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-file-invoice fa-2x text-primary"></i>
                        </div>
                        <h6>إجمالي الفواتير</h6>
                        <p class="text-primary fw-bold">{{ invoices|length }}</p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                        <h6>مدفوعة</h6>
                        <p class="text-success fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                        <h6>معلقة</h6>
                        <p class="text-warning fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        </div>
                        <h6>متأخرة</h6>
                        <p class="text-danger fw-bold">
                            {{ invoices|length }} <!-- سيتم حسابها لاحقاً -->
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير hover للبطاقات
        const cards = document.querySelectorAll('.invoice-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث العداد
        const invoiceCount = {{ invoices|length }};
        document.title = `قائمة الفواتير (${invoiceCount})`;
        
        // تحديث تلقائي كل 5 دقائق
        setInterval(function() {
            // يمكن إضافة تحديث تلقائي هنا
        }, 300000);
    });
</script>
{% endblock %}
