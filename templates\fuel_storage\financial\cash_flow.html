{% extends 'base.html' %}
{% load static %}
{% load financial_filters %}

{% block title %}تقرير التدفق النقدي{% endblock %}

{% block extra_css %}
<style>
    .cash-flow-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .flow-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .flow-title {
        font-size: 2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .flow-period {
        font-size: 1.1rem;
        color: #7f8c8d;
    }
    
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .operating-section .section-header {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .investing-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .financing-section .section-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .flow-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #ecf0f1;
        transition: all 0.3s ease;
    }
    
    .flow-item:hover {
        background-color: #f8f9fa;
        transform: translateX(-3px);
    }
    
    .flow-item:last-child {
        border-bottom: none;
    }
    
    .flow-description {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .flow-date {
        font-size: 0.9rem;
        color: #7f8c8d;
        margin-top: 3px;
    }
    
    .flow-amount {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .inflow {
        color: #27ae60;
    }
    
    .outflow {
        color: #e74c3c;
    }
    
    .subtotal-row {
        background: #f8f9fa;
        border-top: 2px solid #bdc3c7;
        font-weight: bold;
        font-size: 1.2rem;
        margin-top: 10px;
    }
    
    .net-flow-row {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: bold;
        font-size: 1.3rem;
        border-radius: 10px;
        margin-top: 15px;
    }
    
    .positive-flow {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .negative-flow {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .summary-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .summary-card:hover {
        transform: translateY(-5px);
    }
    
    .summary-card .icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    .summary-card .value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .summary-card .label {
        color: #7f8c8d;
        font-size: 0.9rem;
    }
    
    .operating-card .icon { color: #27ae60; }
    .investing-card .icon { color: #3498db; }
    .financing-card .icon { color: #e74c3c; }
    .net-card .icon { color: #f39c12; }
    
    .flow-chart {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .chart-bar {
        height: 30px;
        border-radius: 15px;
        margin: 10px 0;
        position: relative;
        overflow: hidden;
    }
    
    .chart-bar.positive {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .chart-bar.negative {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .chart-label {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: white;
        font-weight: bold;
        font-size: 0.9rem;
    }
    
    @media print {
        .no-print { display: none !important; }
        .cash-flow-container { box-shadow: none; border: 1px solid #ddd; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-exchange-alt me-2"></i>
                        تقرير التدفق النقدي
                    </h1>
                    <p class="text-muted">عرض التدفقات النقدية الداخلة والخارجة للفترة المحددة</p>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-primary me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="row mb-4 no-print">
        <div class="col-md-6">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label for="from_date" class="form-label">من تاريخ</label>
                    <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-5">
                    <label for="to_date" class="form-label">إلى تاريخ</label>
                    <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards no-print">
        <div class="summary-card operating-card">
            <div class="icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="value {% if net_operating >= 0 %}inflow{% else %}outflow{% endif %}">
                {{ net_operating|floatformat:2 }}
            </div>
            <div class="label">صافي التدفق التشغيلي</div>
        </div>
        
        <div class="summary-card investing-card">
            <div class="icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="value {% if net_investing >= 0 %}inflow{% else %}outflow{% endif %}">
                {{ net_investing|floatformat:2 }}
            </div>
            <div class="label">صافي التدفق الاستثماري</div>
        </div>
        
        <div class="summary-card financing-card">
            <div class="icon">
                <i class="fas fa-university"></i>
            </div>
            <div class="value {% if net_financing >= 0 %}inflow{% else %}outflow{% endif %}">
                {{ net_financing|floatformat:2 }}
            </div>
            <div class="label">صافي التدفق التمويلي</div>
        </div>
        
        <div class="summary-card net-card">
            <div class="icon">
                <i class="fas fa-balance-scale"></i>
            </div>
            <div class="value {% if net_cash_flow >= 0 %}inflow{% else %}outflow{% endif %}">
                {{ net_cash_flow|floatformat:2 }}
            </div>
            <div class="label">صافي التدفق النقدي</div>
        </div>
    </div>

    <!-- Visual Chart -->
    <div class="flow-chart no-print">
        <h5 class="mb-3">
            <i class="fas fa-chart-bar me-2"></i>
            مخطط التدفقات النقدية
        </h5>
        
        <div class="mb-3">
            <div class="d-flex justify-content-between mb-1">
                <span>الأنشطة التشغيلية</span>
                <span class="{% if net_operating >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_operating|floatformat:2 }}
                </span>
            </div>
            <div class="chart-bar {% if net_operating >= 0 %}positive{% else %}negative{% endif %}"
                 style="width: {% if net_cash_flow != 0 %}{{ net_operating|abs_value|percentage:net_cash_flow|abs_value }}{% else %}50{% endif %}%;">
                <div class="chart-label">تشغيلي</div>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="d-flex justify-content-between mb-1">
                <span>الأنشطة الاستثمارية</span>
                <span class="{% if net_investing >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_investing|floatformat:2 }}
                </span>
            </div>
            <div class="chart-bar {% if net_investing >= 0 %}positive{% else %}negative{% endif %}"
                 style="width: {% if net_cash_flow != 0 %}{{ net_investing|abs_value|percentage:net_cash_flow|abs_value }}{% else %}30{% endif %}%;">
                <div class="chart-label">استثماري</div>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="d-flex justify-content-between mb-1">
                <span>الأنشطة التمويلية</span>
                <span class="{% if net_financing >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_financing|floatformat:2 }}
                </span>
            </div>
            <div class="chart-bar {% if net_financing >= 0 %}positive{% else %}negative{% endif %}"
                 style="width: {% if net_cash_flow != 0 %}{{ net_financing|abs_value|percentage:net_cash_flow|abs_value }}{% else %}20{% endif %}%;">
                <div class="chart-label">تمويلي</div>
            </div>
        </div>
    </div>

    <!-- Cash Flow Statement -->
    <div class="cash-flow-container">
        <div class="flow-header">
            <div class="flow-title">تقرير التدفق النقدي</div>
            <div class="flow-period">
                للفترة من {{ from_date|date:"j F Y" }} إلى {{ to_date|date:"j F Y" }}
            </div>
        </div>

        <!-- Operating Activities -->
        <div class="operating-section mb-4">
            <div class="section-header">
                <i class="fas fa-cogs me-2"></i>
                الأنشطة التشغيلية
            </div>
            
            <h6 class="mt-3 mb-2 text-success">التدفقات الداخلة:</h6>
            {% for flow in operating_inflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount inflow">
                    +{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات داخلة</div>
            {% endfor %}
            
            <h6 class="mt-3 mb-2 text-danger">التدفقات الخارجة:</h6>
            {% for flow in operating_outflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount outflow">
                    -{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات خارجة</div>
            {% endfor %}
            
            <div class="flow-item subtotal-row">
                <div class="flow-description">صافي التدفق من الأنشطة التشغيلية</div>
                <div class="flow-amount {% if net_operating >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_operating|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Investing Activities -->
        <div class="investing-section mb-4">
            <div class="section-header">
                <i class="fas fa-chart-line me-2"></i>
                الأنشطة الاستثمارية
            </div>
            
            <h6 class="mt-3 mb-2 text-success">التدفقات الداخلة:</h6>
            {% for flow in investing_inflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount inflow">
                    +{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات داخلة</div>
            {% endfor %}
            
            <h6 class="mt-3 mb-2 text-danger">التدفقات الخارجة:</h6>
            {% for flow in investing_outflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount outflow">
                    -{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات خارجة</div>
            {% endfor %}
            
            <div class="flow-item subtotal-row">
                <div class="flow-description">صافي التدفق من الأنشطة الاستثمارية</div>
                <div class="flow-amount {% if net_investing >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_investing|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Financing Activities -->
        <div class="financing-section mb-4">
            <div class="section-header">
                <i class="fas fa-university me-2"></i>
                الأنشطة التمويلية
            </div>
            
            <h6 class="mt-3 mb-2 text-success">التدفقات الداخلة:</h6>
            {% for flow in financing_inflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount inflow">
                    +{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات داخلة</div>
            {% endfor %}
            
            <h6 class="mt-3 mb-2 text-danger">التدفقات الخارجة:</h6>
            {% for flow in financing_outflows %}
            <div class="flow-item">
                <div>
                    <div class="flow-description">{{ flow.description }}</div>
                    <div class="flow-date">{{ flow.date }}</div>
                </div>
                <div class="flow-amount outflow">
                    -{{ flow.amount|floatformat:2 }}
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-3">لا توجد تدفقات خارجة</div>
            {% endfor %}
            
            <div class="flow-item subtotal-row">
                <div class="flow-description">صافي التدفق من الأنشطة التمويلية</div>
                <div class="flow-amount {% if net_financing >= 0 %}inflow{% else %}outflow{% endif %}">
                    {{ net_financing|floatformat:2 }}
                </div>
            </div>
        </div>

        <!-- Net Cash Flow -->
        <div class="flow-item net-flow-row {% if net_cash_flow >= 0 %}positive-flow{% else %}negative-flow{% endif %}">
            <div class="flow-description">صافي التدفق النقدي</div>
            <div class="flow-amount">
                {{ net_cash_flow|floatformat:2 }}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للعناصر
        const flowItems = document.querySelectorAll('.flow-item:not(.subtotal-row):not(.net-flow-row)');
        flowItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f1f2f6';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `التدفق النقدي - {{ from_date|date:"Y/m/d" }} إلى {{ to_date|date:"Y/m/d" }}`;
    });
</script>
{% endblock %}
