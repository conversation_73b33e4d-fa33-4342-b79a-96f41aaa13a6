from django.urls import path
from . import views

app_name = 'fuel_storage'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('storages/', views.storage_list, name='storage_list'),
    path('categories/', views.category_list, name='category_list'),
    path('suppliers/', views.supplier_list, name='supplier_list'),
    path('beneficiaries/', views.beneficiary_list, name='beneficiary_list'),
    path('incoming-operations/', views.incoming_operations_list, name='incoming_operations_list'),
    path('outgoing-operations/', views.outgoing_operations_list, name='outgoing_operations_list'),
    path('operation-modifications/', views.operation_modifications_list, name='operation_modifications_list'),
    path('storage-items/', views.storage_items_list, name='storage_items_list'),
    path('storage-report/', views.storage_report, name='storage_report'),
    path('incoming-returns/', views.incoming_returns_list, name='incoming_returns_list'),
    path('outgoing-returns/', views.outgoing_returns_list, name='outgoing_returns_list'),
    path('returns-summary/', views.returns_summary, name='returns_summary'),
    path('damage-operations/', views.damage_operations_list, name='damage_operations_list'),
    path('storage-transfers/', views.storage_transfers_list, name='storage_transfers_list'),
    path('operations-summary/', views.operations_summary, name='operations_summary'),
    
    # التقارير المتقدمة
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/storage-movement/', views.storage_movement_report, name='storage_movement_report'),
    path('reports/storage-status/', views.storage_status_report, name='storage_status_report'),
]
