from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from fuel_storage.models import SystemLog

class Command(BaseCommand):
    help = 'تنظيف سجلات النظام القديمة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=90,
            help='عدد الأيام للاحتفاظ بالسجلات (افتراضي: 90)'
        )

    def handle(self, *args, **options):
        days = options['days']
        cutoff_date = timezone.now() - timedelta(days=days)
        
        deleted_count = SystemLog.objects.filter(
            timestamp__lt=cutoff_date
        ).delete()[0]
        
        self.stdout.write(
            self.style.SUCCESS(
                f'تم حذف {deleted_count} سجل أقدم من {days} يوم'
            )
        )
