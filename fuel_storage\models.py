from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db.models import ProtectedError
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models import Sum, Q

class CustomUser(AbstractUser):
    USER_TYPES = [
        ('manager', 'مدير'),
        ('operator', 'مشغل'),
        ('accountant', 'محاسب'),
        ('viewer', 'مستعرض'),
    ]
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='operator', verbose_name='نوع المستخدم')
    phone_number = models.Char<PERSON>ield(max_length=17, blank=True, null=True, verbose_name='رقم الهاتف')
    department = models.CharField(max_length=100, blank=True, null=True, verbose_name='القسم')
    
    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

class Category(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم الصنف')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز الصنف')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    minimum_stock_level = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الحد الأدنى للمخزون')
    maximum_stock_level = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الحد الأقصى للمخزون')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if (self.incomingoperationitem_set.exists() or 
            self.outgoingoperationitem_set.exists() or
            self.incomingreturnitem_set.exists() or
            self.outgoingreturnitem_set.exists() or
            self.damageoperationitem_set.exists() or
            self.storagetransferitem_set.exists()):
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا الصنف لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name} ({self.code}){status}"
    
    class Meta:
        verbose_name = 'صنف'
        verbose_name_plural = 'الأصناف'

class PriceList(models.Model):
    """قائمة الأسعار"""
    name = models.CharField(max_length=100, verbose_name='اسم قائمة الأسعار')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    is_active = models.BooleanField(default=True, verbose_name='نشطة')
    effective_date = models.DateField(verbose_name='تاريخ السريان')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئت بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return f"{self.name} - {self.effective_date}"
    
    class Meta:
        verbose_name = 'قائمة أسعار'
        verbose_name_plural = 'قوائم الأسعار'
        ordering = ['-effective_date']

class CategoryPrice(models.Model):
    """أسعار الأصناف"""
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, verbose_name='قائمة الأسعار')
    purchase_price = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='سعر الشراء')
    selling_price = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='سعر البيع')
    wholesale_price = models.DecimalField(max_digits=15, decimal_places=3, blank=True, null=True, verbose_name='سعر الجملة')
    currency = models.CharField(max_length=3, default='SAR', verbose_name='العملة')
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=15.00, verbose_name='معدل الضريبة %')
    
    def get_total_selling_price(self):
        """حساب السعر الإجمالي مع الضريبة"""
        return self.selling_price * (1 + self.tax_rate / 100)
    
    def __str__(self):
        return f"{self.category.name} - {self.selling_price} {self.currency}"
    
    class Meta:
        verbose_name = 'سعر صنف'
        verbose_name_plural = 'أسعار الأصناف'
        unique_together = ['category', 'price_list']

class Station(models.Model):
    name = models.CharField(max_length=100, verbose_name='اسم المحطة')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز المحطة')
    address = models.TextField(verbose_name='العنوان')
    city = models.CharField(max_length=50, verbose_name='المدينة')
    region = models.CharField(max_length=50, verbose_name='المنطقة')
    manager_name = models.CharField(max_length=100, verbose_name='اسم المدير')
    phone_number = models.CharField(max_length=17, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.incomingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذه المحطة لأنها مرتبطة بعمليات. تم إلغاء تفعيلها بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name} ({self.code}){status}"
    
    class Meta:
        verbose_name = 'محطة'
        verbose_name_plural = 'المحطات'

class Supplier(models.Model):
    SUPPLIER_TYPES = [
        ('local', 'محلي'),
        ('international', 'دولي'),
        ('government', 'حكومي'),
    ]
    
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز المورد')
    supplier_type = models.CharField(max_length=20, choices=SUPPLIER_TYPES, default='local', verbose_name='نوع المورد')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='الرقم الضريبي')
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='حد الائتمان')
    payment_terms = models.IntegerField(default=30, verbose_name='شروط الدفع (أيام)')
    rating = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], default=3, verbose_name='التقييم')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.incomingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المورد لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.full_name} ({self.code}){status}"
    
    class Meta:
        verbose_name = 'مورد'
        verbose_name_plural = 'الموردون'

class Beneficiary(models.Model):
    BENEFICIARY_TYPES = [
        ('government', 'حكومي'),
        ('private', 'خاص'),
        ('individual', 'فردي'),
    ]
    
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز المستفيد')
    beneficiary_type = models.CharField(max_length=20, choices=BENEFICIARY_TYPES, default='government', verbose_name='نوع المستفيد')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    contact_person = models.CharField(max_length=100, blank=True, null=True, verbose_name='الشخص المسؤول')
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='حد الائتمان')
    discount_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name='معدل الخصم %')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.outgoingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المستفيد لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.full_name} ({self.code}){status}"
    
    class Meta:
        verbose_name = 'مستفيد'
        verbose_name_plural = 'المستفيدون'

class Storage(models.Model):
    CLASSIFICATION_CHOICES = [
        ('main', 'مخزن رئيسي'),
        ('secondary', 'مخزن فرعي'),
        ('temporary', 'مخزن مؤقت'),
        ('emergency', 'مخزن طوارئ'),
    ]
    
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم المخزن')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز المخزن')
    classification = models.CharField(max_length=20, choices=CLASSIFICATION_CHOICES, verbose_name='التصنيف')
    location = models.TextField(verbose_name='الموقع')
    capacity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='السعة الإجمالية')
    keeper_name = models.CharField(max_length=100, verbose_name='أمين المخزن')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='هاتف المخزن')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    temperature_controlled = models.BooleanField(default=False, verbose_name='مكيف الحرارة')
    security_level = models.CharField(max_length=20, choices=[
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
    ], default='medium', verbose_name='مستوى الأمان')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def get_current_capacity_usage(self):
        """حساب نسبة استخدام السعة الحالية"""
        total_quantity = sum(item.current_quantity for item in self.storageitem_set.all())
        if self.capacity > 0:
            return (total_quantity / self.capacity) * 100
        return 0
    
    def delete(self, *args, **kwargs):
        if (self.incomingoperation_set.exists() or 
            self.outgoingoperation_set.exists() or
            self.damageoperation_set.exists() or
            self.transfers_from.exists() or
            self.transfers_to.exists()):
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المخزن لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name} ({self.code}){status}"
    
    class Meta:
        verbose_name = 'مخزن'
        verbose_name_plural = 'المخازن'

class StorageItem(models.Model):
    UNIT_CHOICES = [
        ('liter', 'لتر'),
        ('gallon', 'جالون'),
        ('barrel', 'برميل'),
        ('ton', 'طن'),
        ('kg', 'كيلوجرام'),
        ('cubic_meter', 'متر مكعب'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    unit_of_measure = models.CharField(max_length=20, choices=UNIT_CHOICES, verbose_name='وحدة القياس')
    opening_balance = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الرصيد الافتتاحي')
    current_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الكمية الحالية')
    reserved_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الكمية المحجوزة')
    reorder_point = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='نقطة إعادة الطلب')
    last_purchase_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='آخر سعر شراء')
    average_cost = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='متوسط التكلفة')
    location_in_storage = models.CharField(max_length=50, blank=True, null=True, verbose_name='الموقع في المخزن')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الدفعة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    def get_available_quantity(self):
        """الكمية المتاحة (الحالية - المحجوزة)"""
        return self.current_quantity - self.reserved_quantity
    
    def is_low_stock(self):
        """فحص إذا كان المخزون منخفض"""
        return (self.category.minimum_stock_level > 0 and 
                self.current_quantity <= self.category.minimum_stock_level)
    
    def is_expired(self):
        """فحص إذا كان الصنف منتهي الصلاحية"""
        if self.expiry_date:
            return self.expiry_date < timezone.now().date()
        return False
    
    def days_until_expiry(self):
        """عدد الأيام حتى انتهاء الصلاحية"""
        if self.expiry_date:
            delta = self.expiry_date - timezone.now().date()
            return delta.days
        return None
    
    def clean(self):
        """التحقق من صحة البيانات"""
        if self.current_quantity < 0:
            raise ValidationError("الكمية الحالية لا يمكن أن تكون سالبة")
        if self.reserved_quantity < 0:
            raise ValidationError("الكمية المحجوزة لا يمكن أن تكون سالبة")
        if self.reserved_quantity > self.current_quantity:
            raise ValidationError("الكمية المحجوزة لا يمكن أن تتجاوز الكمية الحالية")
    
    def save(self, *args, **kwargs):
        """حفظ مع التحقق من صحة البيانات"""
        self.clean()
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.storage.name} - {self.category.name} ({self.current_quantity} {self.unit_of_measure})"
    
    class Meta:
        verbose_name = 'صنف مخزن'
        verbose_name_plural = 'أصناف المخازن'
        unique_together = ['storage', 'category']

class StorageTransferFile(models.Model):
    storage_transfer = models.ForeignKey('StorageTransfer', on_delete=models.CASCADE, related_name='files', verbose_name='عملية التحويل')
    file = models.FileField(upload_to='storage_transfers/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    file_type = models.CharField(max_length=20, choices=[
        ('transfer_order', 'أمر نقل'),
        ('receipt', 'إيصال'),
        ('inspection', 'فحص'),
        ('insurance', 'تأمين'),
        ('other', 'أخرى'),
    ], default='other', verbose_name='نوع الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق للتحويل'
        verbose_name_plural = 'الملفات المرفقة للتحويل'

class InventoryAdjustment(models.Model):
    """تسوية المخزون"""
    ADJUSTMENT_TYPES = [
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('correction', 'تصحيح'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    adjustment_date = models.DateTimeField(verbose_name='تاريخ التسوية')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES, verbose_name='نوع التسوية')
    reason = models.TextField(verbose_name='السبب')
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='القيمة الإجمالية')
    is_approved = models.BooleanField(default=False, verbose_name='معتمد')
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_adjustments', verbose_name='اعتمد بواسطة')
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return f"تسوية {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'تسوية مخزون'
        verbose_name_plural = 'تسويات المخزون'

class InventoryAdjustmentItem(models.Model):
    adjustment = models.ForeignKey(InventoryAdjustment, on_delete=models.CASCADE, related_name='items', verbose_name='التسوية')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    book_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية الدفترية')
    physical_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية الفعلية')
    difference = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الفرق')
    unit_cost = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='تكلفة الوحدة')
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='القيمة الإجمالية')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def save(self, *args, **kwargs):
        # حساب الفرق
        self.difference = self.physical_quantity - self.book_quantity
        
        # حساب القيمة الإجمالية
        self.total_value = abs(self.difference) * self.unit_cost
        
        super().save(*args, **kwargs)
        
        # تحديث الكمية في المخزن إذا كانت التسوية معتمدة
        if self.adjustment.is_approved:
            try:
                storage_item = StorageItem.objects.get(
                    storage=self.adjustment.storage,
                    category=self.category
                )
                storage_item.current_quantity = self.physical_quantity
                storage_item.save()
            except StorageItem.DoesNotExist:
                pass
    
    def __str__(self):
        return f"{self.category.name} - فرق: {self.difference}"
    
    class Meta:
        verbose_name = 'صنف تسوية'
        verbose_name_plural = 'أصناف التسوية'

class MaintenanceSchedule(models.Model):
    """جدولة الصيانة"""
    MAINTENANCE_TYPES = [
        ('preventive', 'وقائية'),
        ('corrective', 'تصحيحية'),
        ('emergency', 'طارئة'),
        ('routine', 'دورية'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'مجدولة'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
        ('postponed', 'مؤجلة'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPES, verbose_name='نوع الصيانة')
    scheduled_date = models.DateTimeField(verbose_name='تاريخ الصيانة المجدولة')
    actual_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الصيانة الفعلي')
    duration_hours = models.DecimalField(max_digits=5, decimal_places=2, verbose_name='المدة بالساعات')
    description = models.TextField(verbose_name='الوصف')
    technician_name = models.CharField(max_length=100, verbose_name='اسم الفني')
    cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='التكلفة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled', verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def is_overdue(self):
        """التحقق من تأخر الصيانة"""
        if self.status == 'scheduled':
            return self.scheduled_date < timezone.now()
        return False
    
    def __str__(self):
        return f"صيانة {self.get_maintenance_type_display()} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'جدولة صيانة'
        verbose_name_plural = 'جدولة الصيانة'
        ordering = ['scheduled_date']

class QualityControl(models.Model):
    """مراقبة الجودة"""
    INSPECTION_TYPES = [
        ('incoming', 'فحص الوارد'),
        ('periodic', 'فحص دوري'),
        ('random', 'فحص عشوائي'),
        ('complaint', 'فحص شكوى'),
    ]
    
    QUALITY_STATUS = [
        ('passed', 'مقبول'),
        ('failed', 'مرفوض'),
        ('conditional', 'مشروط'),
        ('pending', 'قيد المراجعة'),
    ]
    
    storage_item = models.ForeignKey(StorageItem, on_delete=models.CASCADE, verbose_name='صنف المخزن')
    inspection_date = models.DateTimeField(verbose_name='تاريخ الفحص')
    inspection_type = models.CharField(max_length=20, choices=INSPECTION_TYPES, verbose_name='نوع الفحص')
    inspector_name = models.CharField(max_length=100, verbose_name='اسم المفتش')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الدفعة')
    sample_size = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='حجم العينة')
    
    # نتائج الفحص
    density = models.DecimalField(max_digits=8, decimal_places=4, blank=True, null=True, verbose_name='الكثافة')
    viscosity = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, verbose_name='اللزوجة')
    flash_point = models.DecimalField(max_digits=6, decimal_places=1, blank=True, null=True, verbose_name='نقطة الوميض')
    water_content = models.DecimalField(max_digits=5, decimal_places=3, blank=True, null=True, verbose_name='محتوى الماء %')
    sulfur_content = models.DecimalField(max_digits=5, decimal_places=3, blank=True, null=True, verbose_name='محتوى الكبريت %')
    
    quality_status = models.CharField(max_length=20, choices=QUALITY_STATUS, verbose_name='حالة الجودة')
    overall_grade = models.CharField(max_length=5, choices=[
        ('A+', 'ممتاز'),
        ('A', 'ممتاز'),
        ('B+', 'جيد'),
        ('B', 'جيد'),
        ('C', 'مقبول'),
        ('D', 'ضعيف'),
        ('F', 'راسب'),
    ], verbose_name='التقدير العام')
    
    recommendations = models.TextField(blank=True, null=True, verbose_name='التوصيات')
    corrective_actions = models.TextField(blank=True, null=True, verbose_name='الإجراءات التصحيحية')
    next_inspection_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الفحص القادم')
    
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return f"فحص جودة - {self.storage_item} - {self.inspection_date.date()}"
    
    class Meta:
        verbose_name = 'مراقبة جودة'
        verbose_name_plural = 'مراقبة الجودة'
        ordering = ['-inspection_date']

class SystemLog(models.Model):
    """سجل النظام"""
    ACTION_TYPES = [
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('approve', 'اعتماد'),
        ('reject', 'رفض'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),

    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, verbose_name='المستخدم')
    action = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name='الإجراء')
    model_name = models.CharField(max_length=50, verbose_name='اسم النموذج')
    object_id = models.CharField(max_length=50, blank=True, null=True, verbose_name='معرف الكائن')
    description = models.TextField(verbose_name='الوصف')
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')
    user_agent = models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='الوقت')
    
    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.model_name}"
    
    class Meta:
        verbose_name = 'سجل النظام'
        verbose_name_plural = 'سجلات النظام'
        ordering = ['-timestamp']

class StockAlert(models.Model):
    ALERT_TYPES = [
        ('low_stock', 'مخزون منخفض'),
        ('out_of_stock', 'نفاد المخزون'),
        ('expiry_warning', 'تحذير انتهاء الصلاحية'),
        ('expired', 'منتهي الصلاحية'),
        ('overstock', 'مخزون زائد'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('critical', 'حرج'),
    ]
    
    storage_item = models.ForeignKey(StorageItem, on_delete=models.CASCADE, verbose_name='صنف المخزن')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES, verbose_name='نوع التنبيه')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, verbose_name='الأولوية')
    message = models.TextField(verbose_name='رسالة التنبيه')
    is_read = models.BooleanField(default=False, verbose_name='مقروء')
    is_resolved = models.BooleanField(default=False, verbose_name='محلول')
    resolved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, blank=True, null=True, verbose_name='حل بواسطة')
    resolved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.storage_item}"
    
    class Meta:
        verbose_name = 'تنبيه مخزون'
        verbose_name_plural = 'تنبيهات المخزون'
        ordering = ['-created_at']

# Operation Models
class IncomingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_date = models.DateTimeField(verbose_name='تاريخ عملية الوارد')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, verbose_name='المورد')
    station = models.ForeignKey(Station, on_delete=models.PROTECT, verbose_name='المحطة')
    supply_document_number = models.CharField(max_length=50, verbose_name='رقم سند التوريد')
    invoice_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الفاتورة')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    # المبالغ المالية
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الضريبة')
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الخصم')
    net_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الصافي')
    
    statement = models.TextField(verbose_name='البيان')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    is_approved = models.BooleanField(default=False, verbose_name='معتمد')
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_incoming_operations', verbose_name='اعتمد بواسطة')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        # قفل العملية بعد الحفظ الأول
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"وارد {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية وارد'
        verbose_name_plural = 'عمليات الوارد'

class IncomingOperationItem(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية الوارد')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    imported_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المستوردة')
    unit_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='سعر الوحدة')
    total_price = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='السعر الإجمالي')
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name='نسبة الخصم %')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الدفعة')
    quality_grade = models.CharField(max_length=20, choices=[
        ('A', 'ممتاز'),
        ('B', 'جيد'),
        ('C', 'مقبول'),
    ], default='A', verbose_name='درجة الجودة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.incoming_operation.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # حساب السعر الإجمالي
        discount_amount = (self.unit_price * self.imported_quantity * self.discount_percentage) / 100
        self.total_price = (self.unit_price * self.imported_quantity) - discount_amount
        
        # تحديث كمية المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.incoming_operation.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',  # افتراضي
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        
        if not created:
            storage_item.current_quantity += self.imported_quantity
            storage_item.save()
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.imported_quantity}"
    
    class Meta:
        verbose_name = 'صنف وارد'
        verbose_name_plural = 'أصناف الوارد'

class OutgoingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_date = models.DateTimeField(verbose_name='تاريخ عملية الصادر')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    beneficiary = models.ForeignKey(Beneficiary, on_delete=models.PROTECT, verbose_name='المستفيد')
    invoice_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الفاتورة')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    # المبالغ المالية
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الضريبة')
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الخصم')
    net_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الصافي')
    
    statement = models.TextField(verbose_name='البيان')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    is_approved = models.BooleanField(default=False, verbose_name='معتمد')
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_outgoing_operations', verbose_name='اعتمد بواسطة')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"صادر {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية صادر'
        verbose_name_plural = 'عمليات الصادر'

class OutgoingOperationItem(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية الصادر')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    exported_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المصروفة')
    unit_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='سعر الوحدة')
    total_price = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='السعر الإجمالي')
    transfer_date = models.DateField(verbose_name='تاريخ التحويل')
    actual_transfer_date = models.DateField(blank=True, null=True, verbose_name='تاريخ التحويل الفعلي')
    cost_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='سعر التكلفة')
    profit_margin = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='هامش الربح')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.outgoing_operation.storage,
                category=self.category
            )
            if storage_item.current_quantity < self.exported_quantity:
                raise ValidationError(f'الكمية المطلوبة ({self.exported_quantity}) أكبر من الكمية المتوفرة ({storage_item.current_quantity})')
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.outgoing_operation.storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.outgoing_operation.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # حساب السعر الإجمالي
        self.total_price = self.unit_price * self.exported_quantity
        
        # حساب هامش الربح
        total_cost = self.cost_price * self.exported_quantity
        self.profit_margin = self.total_price - total_cost
        
        # تحديث كمية المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.outgoing_operation.storage,
                category=self.category
            )
            # تحديث الكمية الحالية
            storage_item.current_quantity -= self.exported_quantity
            
            # التحقق من أن الكمية لا تكون سالبة
            if storage_item.current_quantity < 0:
                storage_item.current_quantity = 0
            
            storage_item.save()
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.outgoing_operation.storage.name}')
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.exported_quantity}"
    
    class Meta:
        verbose_name = 'صنف صادر'
        verbose_name_plural = 'أصناف الصادر'

class DamageOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    damage_date = models.DateTimeField(verbose_name='تاريخ التلف')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"تلف {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية تلف'
        verbose_name_plural = 'عمليات التلف'

class DamageOperationItem(models.Model):
    DAMAGE_REASONS = [
        ('expired', 'منتهي الصلاحية'),
        ('contaminated', 'ملوث'),
        ('leaked', 'تسرب'),
        ('fire', 'حريق'),
        ('accident', 'حادث'),
        ('quality_issue', 'مشكلة في الجودة'),
        ('other', 'أخرى'),
    ]
    
    damage_operation = models.ForeignKey(DamageOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية التلف')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    damaged_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية التالفة')
    reason = models.CharField(max_length=50, choices=DAMAGE_REASONS, verbose_name='السبب')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.damage_operation.storage,
                category=self.category
            )
            if storage_item.current_quantity < self.damaged_quantity:
                raise ValidationError(f'الكمية المطلوبة ({self.damaged_quantity}) أكبر من الكمية المتوفرة ({storage_item.current_quantity})')
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.damage_operation.storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.damage_operation.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # تحديث كمية المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.damage_operation.storage,
                category=self.category
            )
            # تحديث الكمية الحالية
            storage_item.current_quantity -= self.damaged_quantity
            
            # التحقق من أن الكمية لا تكون سالبة
            if storage_item.current_quantity < 0:
                storage_item.current_quantity = 0
            
            storage_item.save()
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.damage_operation.storage.name}')
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.damaged_quantity}"
    
    class Meta:
        verbose_name = 'صنف تالف'
        verbose_name_plural = 'أصناف التلف'

class StorageTransfer(models.Model):
    from_storage = models.ForeignKey(Storage, on_delete=models.PROTECT, related_name='transfers_from', verbose_name='المخزن المحول منه')
    to_storage = models.ForeignKey(Storage, on_delete=models.PROTECT, related_name='transfers_to', verbose_name='المخزن المحول إليه')
    transfer_date = models.DateTimeField(verbose_name='تاريخ النقل')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    # تكاليف النقل
    transfer_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='تكلفة النقل')
    insurance_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='تكلفة التأمين')
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='التكلفة الإجمالية')
    
    # تفاصيل النقل
    transfer_method = models.CharField(max_length=50, choices=[
        ('truck', 'شاحنة'),
        ('pipeline', 'أنبوب'),
        ('rail', 'سكة حديد'),
        ('ship', 'سفينة'),
        ('other', 'أخرى'),
    ], default='truck', verbose_name='طريقة النقل')
    priority = models.CharField(max_length=20, choices=[
        ('low', 'منخفض'),
        ('normal', 'عادي'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    ], default='normal', verbose_name='الأولوية')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    is_approved = models.BooleanField(default=False, verbose_name='معتمد')
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_transfers', verbose_name='اعتمد بواسطة')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def clean(self):
        # التحقق من أن المخزن المصدر مختلف عن المخزن الهدف
        if self.from_storage == self.to_storage:
            raise ValidationError("لا يمكن التحويل من وإلى نفس المخزن")
    
    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية
        self.total_cost = self.transfer_cost + self.insurance_cost
        
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"تحويل {self.paper_number} - من {self.from_storage.name} إلى {self.to_storage.name}"
    
    class Meta:
        verbose_name = 'عملية تحويل مخزني'
        verbose_name_plural = 'عمليات التحويل المخزني'

class StorageTransferItem(models.Model):
    TRANSFER_REASONS = [
        ('rebalancing', 'إعادة توزيع'),
        ('maintenance', 'صيانة المخزن'),
        ('capacity', 'سعة المخزن'),
        ('location', 'قرب الموقع'),
        ('emergency', 'حالة طوارئ'),
        ('optimization', 'تحسين التشغيل'),
        ('demand', 'طلب السوق'),
        ('cost_reduction', 'تقليل التكلفة'),
        ('quality_control', 'مراقبة الجودة'),
        ('other', 'أخرى'),
    ]
    
    storage_transfer = models.ForeignKey(StorageTransfer, on_delete=models.CASCADE, related_name='items', verbose_name='عملية التحويل')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    transferred_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المحولة')
    unit_cost = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='تكلفة الوحدة')
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='القيمة الإجمالية')
    reason = models.CharField(max_length=50, choices=TRANSFER_REASONS, verbose_name='السبب')
    condition_before = models.CharField(max_length=20, choices=[
        ('excellent', 'ممتاز'),
        ('good', 'جيد'),
        ('fair', 'مقبول'),
        ('poor', 'ضعيف'),
    ], default='good', verbose_name='الحالة قبل النقل')
    condition_after = models.CharField(max_length=20, choices=[
        ('excellent', 'ممتاز'),
        ('good', 'جيد'),
        ('fair', 'مقبول'),
        ('poor', 'ضعيف'),
        ('damaged', 'تالف'),
    ], blank=True, null=True, verbose_name='الحالة بعد النقل')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم الدفعة')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن المصدر
        try:
            source_storage_item = StorageItem.objects.get(
                storage=self.storage_transfer.from_storage,
                category=self.category
            )
            if source_storage_item.current_quantity < self.transferred_quantity:
                raise ValidationError(f'الكمية المطلوبة ({self.transferred_quantity}) أكبر من الكمية المتوفرة ({source_storage_item.current_quantity})')
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن المصدر {self.storage_transfer.from_storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.storage_transfer.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # حساب القيمة الإجمالية
        self.total_value = self.unit_cost * self.transferred_quantity
        
        # تحديث كمية المخزن المصدر
        source_storage_item, created = StorageItem.objects.get_or_create(
            storage=self.storage_transfer.from_storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        # تحديث كمية المخزن المصدر
        source_storage_item.current_quantity -= self.transferred_quantity
        
        # التحقق من أن الكمية لا تكون سالبة
        if source_storage_item.current_quantity < 0:
            source_storage_item.current_quantity = 0
        
        source_storage_item.save()
        
        # تحديث كمية المخزن الهدف
        target_storage_item, created = StorageItem.objects.get_or_create(
            storage=self.storage_transfer.to_storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        target_storage_item.current_quantity += self.transferred_quantity
        
        # التحقق من أن الكمية لا تكون سالبة
        if target_storage_item.current_quantity < 0:
            target_storage_item.current_quantity = 0
        
        target_storage_item.save()
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.transferred_quantity}"
    
    class Meta:
        verbose_name = 'صنف محول'
        verbose_name_plural = 'أصناف التحويل'

class OperationModification(models.Model):
    OPERATION_TYPES = [
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES, verbose_name='نوع العملية')
    operation_date = models.DateTimeField(verbose_name='تاريخ العملية')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    
    # الكميات
    previous_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية السابقة')
    new_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية الجديدة')
    
    # ربط بالعمليات الأصلية
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الوارد')
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الصادر')
    
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    modified_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='عُدل بواسطة')
    modification_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعديل')
    
    def clean(self):
        # التحقق من أن نوع العملية يتطابق مع العملية المرتبطة
        if self.operation_type == 'incoming' and not self.incoming_operation:
            raise ValidationError("يجب تحديد عملية الوارد عند اختيار نوع العملية وارد")
        if self.operation_type == 'outgoing' and not self.outgoing_operation:
            raise ValidationError("يجب تحديد عملية الصادر عند اختيار نوع العملية صادر")
    
    def save(self, *args, **kwargs):
        self.clean()
        
        # تحديث كمية المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        
        # حساب الفرق وتطبيقه
        difference = self.new_quantity - self.previous_quantity
        storage_item.current_quantity += difference
        storage_item.save()
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"تعديل {self.get_operation_type_display()} - {self.category.name} - {self.modification_date.date()}"
    
    class Meta:
        verbose_name = 'تعديل عملية'
        verbose_name_plural = 'تعديلات العمليات'

class IncomingReturn(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.PROTECT, verbose_name='عملية الوارد المراد الإرجاع منها')
    return_date = models.DateTimeField(verbose_name='تاريخ المرتجع')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    # المبالغ المالية
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    refund_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الاسترداد')
    
    statement = models.TextField(verbose_name='البيان')
    return_reason = models.TextField(verbose_name='سبب الإرجاع')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    # الحقول التي تُعبأ تلقائياً من عملية الوارد
    @property
    def incoming_date(self):
        return self.incoming_operation.operation_date
    
    @property
    def storage(self):
        return self.incoming_operation.storage
    
    @property
    def supplier(self):
        return self.incoming_operation.supplier
    
    @property
    def station(self):
        return self.incoming_operation.station
    
    def __str__(self):
        return f"مرتجع وارد {self.paper_number} - {self.incoming_operation.paper_number}"
    
    class Meta:
        verbose_name = 'مرتجع وارد'
        verbose_name_plural = 'مرتجعات الوارد'

class IncomingReturnItem(models.Model):
    incoming_return = models.ForeignKey(IncomingReturn, on_delete=models.CASCADE, related_name='items', verbose_name='مرتجع الوارد')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    returned_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المرتجعة')
    unit_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='سعر الوحدة')
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    expected_return_date = models.DateField(verbose_name='تاريخ الرد المفترض')
    actual_return_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')
    return_reason = models.CharField(max_length=100, choices=[
        ('defective', 'معيب'),
        ('expired', 'منتهي الصلاحية'),
        ('wrong_specification', 'مواصفات خاطئة'),
        ('excess_quantity', 'كمية زائدة'),
        ('other', 'أخرى'),
    ], verbose_name='سبب الإرجاع')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من أن الكمية المرتجعة لا تتجاوز الكمية الأصلية في عملية الوارد
        try:
            original_item = IncomingOperationItem.objects.get(
                incoming_operation=self.incoming_return.incoming_operation,
                category=self.category
            )
            
            # حساب إجمالي الكميات المرتجعة سابقاً لهذا الصنف
            previous_returns = IncomingReturnItem.objects.filter(
                incoming_return__incoming_operation=self.incoming_return.incoming_operation,
                category=self.category
            ).exclude(id=self.id if self.id else None)
            
            total_previous_returns = sum(item.returned_quantity for item in previous_returns)
            
            if (total_previous_returns + self.returned_quantity) > original_item.imported_quantity:
                raise ValidationError(
                    f'إجمالي الكميات المرتجعة ({total_previous_returns + self.returned_quantity}) '
                    f'يتجاوز الكمية الأصلية ({original_item.imported_quantity})'
                )
        except IncomingOperationItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في عملية الوارد المحددة')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.incoming_return.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # حساب المبلغ الإجمالي
        self.total_amount = self.unit_price * self.returned_quantity
        
        # تحديث كمية المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.incoming_return.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        
        # تحديث الكمية الحالية
        storage_item.current_quantity -= self.returned_quantity
        
        # التحقق من أن الكمية لا تكون سالبة
        if storage_item.current_quantity < 0:
            storage_item.current_quantity = 0
        
        storage_item.save()
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.returned_quantity}"
    
    class Meta:
        verbose_name = 'صنف مرتجع وارد'
        verbose_name_plural = 'أصناف مرتجع الوارد'

class OutgoingReturn(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.PROTECT, verbose_name='عملية الصادر المراد الإرجاع منها')
    return_date = models.DateTimeField(verbose_name='تاريخ المرتجع')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    # المبالغ المالية
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='مبلغ الائتمان')
    
    statement = models.TextField(verbose_name='البيان')
    return_reason = models.TextField(verbose_name='سبب الإرجاع')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    # الحقول التي تُعبأ تلقائياً من عملية الصادر
    @property
    def outgoing_date(self):
        return self.outgoing_operation.operation_date
    
    @property
    def storage(self):
        return self.outgoing_operation.storage
    
    @property
    def beneficiary(self):
        return self.outgoing_operation.beneficiary
    
    def __str__(self):
        return f"مرتجع صادر {self.paper_number} - {self.outgoing_operation.paper_number}"
    
    class Meta:
        verbose_name = 'مرتجع صادر'
        verbose_name_plural = 'مرتجعات الصادر'

class OutgoingReturnItem(models.Model):
    outgoing_return = models.ForeignKey(OutgoingReturn, on_delete=models.CASCADE, related_name='items', verbose_name='مرتجع الصادر')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    returned_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المرتجعة')
    unit_price = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='سعر الوحدة')
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name='المبلغ الإجمالي')
    expected_return_date = models.DateField(verbose_name='تاريخ الرد المفترض')
    actual_return_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')
    return_reason = models.CharField(max_length=100, choices=[
        ('unused', 'غير مستخدم'),
        ('defective', 'معيب'),
        ('wrong_specification', 'مواصفات خاطئة'),
        ('excess_quantity', 'كمية زائدة'),
        ('other', 'أخرى'),
    ], verbose_name='سبب الإرجاع')
    condition = models.CharField(max_length=20, choices=[
        ('new', 'جديد'),
        ('good', 'جيد'),
        ('damaged', 'تالف'),
    ], default='good', verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من أن الكمية المرتجعة لا تتجاوز الكمية الأصلية في عملية الصادر
        try:
            original_item = OutgoingOperationItem.objects.get(
                outgoing_operation=self.outgoing_return.outgoing_operation,
                category=self.category
            )
            
            # حساب إجمالي الكميات المرتجعة سابقاً لهذا الصنف
            previous_returns = OutgoingReturnItem.objects.filter(
                outgoing_return__outgoing_operation=self.outgoing_return.outgoing_operation,
                category=self.category
            ).exclude(id=self.id if self.id else None)
            
            total_previous_returns = sum(item.returned_quantity for item in previous_returns)
            
            if (total_previous_returns + self.returned_quantity) > original_item.exported_quantity:
                raise ValidationError(
                    f'إجمالي الكميات المرتجعة ({total_previous_returns + self.returned_quantity}) '
                    f'يتجاوز الكمية الأصلية ({original_item.exported_quantity})'
                )
        except OutgoingOperationItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في عملية الصادر المحددة')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.outgoing_return.is_locked:
            raise ValidationError("لا يمكن تعديل عملية مقفلة")
        
        # حساب المبلغ الإجمالي
        self.total_amount = self.unit_price * self.returned_quantity
        
        # تحديث كمية المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.outgoing_return.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        
        # تحديث الكمية الحالية
        storage_item.current_quantity += self.returned_quantity
        
        # التحقق من أن الكمية لا تكون سالبة
        if storage_item.current_quantity < 0:
            storage_item.current_quantity = 0
        
        storage_item.save()
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.category.name} - {self.returned_quantity}"
    
    class Meta:
        verbose_name = 'صنف مرتجع صادر'
        verbose_name_plural = 'أصناف مرتجع الصادر'


# ================================
# نماذج النظام المالي والمحاسبة
# ================================

class AccountType(models.Model):
    """أنواع الحسابات المحاسبية"""
    ACCOUNT_TYPES = [
        ('asset', 'أصول'),
        ('liability', 'خصوم'),
        ('equity', 'حقوق الملكية'),
        ('revenue', 'إيرادات'),
        ('expense', 'مصروفات'),
        ('cost_of_goods', 'تكلفة البضاعة المباعة'),
    ]

    name = models.CharField(max_length=100, verbose_name='اسم نوع الحساب')
    code = models.CharField(max_length=10, unique=True, verbose_name='رمز النوع')
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES, verbose_name='نوع الحساب')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    is_active = models.BooleanField(default=True, verbose_name='نشط')

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = 'نوع حساب'
        verbose_name_plural = 'أنواع الحسابات'

class Account(models.Model):
    """الحسابات المحاسبية"""
    account_type = models.ForeignKey(AccountType, on_delete=models.PROTECT, verbose_name='نوع الحساب')
    name = models.CharField(max_length=200, verbose_name='اسم الحساب')
    code = models.CharField(max_length=20, unique=True, verbose_name='رمز الحساب')
    parent_account = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, verbose_name='الحساب الأب')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    opening_balance = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الرصيد الافتتاحي')
    current_balance = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الرصيد الحالي')
    currency = models.CharField(max_length=3, default='SAR', verbose_name='العملة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def get_balance(self, date=None):
        """حساب الرصيد في تاريخ معين"""
        if date is None:
            date = timezone.now().date()

        # حساب مجموع المدين والدائن
        transactions = self.transaction_set.filter(transaction_date__lte=date)
        debit_total = transactions.aggregate(total=Sum('debit_amount'))['total'] or 0
        credit_total = transactions.aggregate(total=Sum('credit_amount'))['total'] or 0

        # حساب الرصيد حسب نوع الحساب
        if self.account_type.account_type in ['asset', 'expense', 'cost_of_goods']:
            return self.opening_balance + debit_total - credit_total
        else:  # liability, equity, revenue
            return self.opening_balance + credit_total - debit_total

    def update_current_balance(self):
        """تحديث الرصيد الحالي"""
        self.current_balance = self.get_balance()
        self.save(update_fields=['current_balance'])

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = 'حساب'
        verbose_name_plural = 'الحسابات'
        ordering = ['code']

class AccountingPeriod(models.Model):
    """الفترات المحاسبية"""
    name = models.CharField(max_length=100, verbose_name='اسم الفترة')
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ النهاية')
    is_closed = models.BooleanField(default=False, verbose_name='مغلقة')
    is_current = models.BooleanField(default=False, verbose_name='الفترة الحالية')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    closed_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')

    def clean(self):
        # التأكد من عدم تداخل الفترات
        overlapping = AccountingPeriod.objects.filter(
            Q(start_date__lte=self.end_date) & Q(end_date__gte=self.start_date)
        ).exclude(pk=self.pk)

        if overlapping.exists():
            raise ValidationError('لا يمكن أن تتداخل الفترات المحاسبية')

        # التأكد من وجود فترة حالية واحدة فقط
        if self.is_current:
            AccountingPeriod.objects.filter(is_current=True).exclude(pk=self.pk).update(is_current=False)

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def close_period(self, user):
        """إغلاق الفترة المحاسبية"""
        if self.is_closed:
            raise ValidationError('الفترة مغلقة بالفعل')

        self.is_closed = True
        self.is_current = False
        self.closed_at = timezone.now()
        self.save()

        # إنشاء قيود الإقفال
        self._create_closing_entries(user)

    def _create_closing_entries(self, user):
        """إنشاء قيود الإقفال"""
        # سيتم تنفيذها لاحقاً
        pass

    def __str__(self):
        status = " (مغلقة)" if self.is_closed else " (مفتوحة)"
        current = " (حالية)" if self.is_current else ""
        return f"{self.name}{status}{current}"

    class Meta:
        verbose_name = 'فترة محاسبية'
        verbose_name_plural = 'الفترات المحاسبية'
        ordering = ['-start_date']

class JournalEntry(models.Model):
    """قيود اليومية"""
    ENTRY_TYPES = [
        ('manual', 'يدوي'),
        ('automatic', 'تلقائي'),
        ('adjustment', 'تسوية'),
        ('closing', 'إقفال'),
    ]

    entry_number = models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')
    entry_date = models.DateField(verbose_name='تاريخ القيد')
    entry_type = models.CharField(max_length=20, choices=ENTRY_TYPES, default='manual', verbose_name='نوع القيد')
    description = models.TextField(verbose_name='البيان')
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم المرجع')
    total_amount = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='إجمالي المبلغ')
    accounting_period = models.ForeignKey(AccountingPeriod, on_delete=models.PROTECT, verbose_name='الفترة المحاسبية')
    is_posted = models.BooleanField(default=False, verbose_name='مرحل')
    is_reversed = models.BooleanField(default=False, verbose_name='معكوس')

    # ربط بالعمليات الأصلية
    incoming_operation = models.ForeignKey('IncomingOperation', on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الوارد')
    outgoing_operation = models.ForeignKey('OutgoingOperation', on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الصادر')
    incoming_return = models.ForeignKey('IncomingReturn', on_delete=models.CASCADE, blank=True, null=True, verbose_name='مرتجع الوارد')
    outgoing_return = models.ForeignKey('OutgoingReturn', on_delete=models.CASCADE, blank=True, null=True, verbose_name='مرتجع الصادر')

    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    posted_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, blank=True, null=True, related_name='posted_entries', verbose_name='رحل بواسطة')
    posted_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الترحيل')

    def clean(self):
        # التأكد من توازن القيد
        if self.pk:
            debit_total = self.transactions.aggregate(total=Sum('debit_amount'))['total'] or 0
            credit_total = self.transactions.aggregate(total=Sum('credit_amount'))['total'] or 0

            if debit_total != credit_total:
                raise ValidationError('يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن')

            self.total_amount = debit_total

    def post(self, user):
        """ترحيل القيد"""
        if self.is_posted:
            raise ValidationError('القيد مرحل بالفعل')

        if self.accounting_period.is_closed:
            raise ValidationError('لا يمكن ترحيل قيود في فترة مغلقة')

        self.clean()

        # ترحيل المعاملات وتحديث أرصدة الحسابات
        for transaction in self.transactions.all():
            transaction.account.update_current_balance()

        self.is_posted = True
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()

    def reverse(self, user, reason):
        """عكس القيد"""
        if not self.is_posted:
            raise ValidationError('لا يمكن عكس قيد غير مرحل')

        if self.is_reversed:
            raise ValidationError('القيد معكوس بالفعل')

        # إنشاء قيد عكسي
        reversed_entry = JournalEntry.objects.create(
            entry_number=f"{self.entry_number}-REV",
            entry_date=timezone.now().date(),
            entry_type='adjustment',
            description=f"عكس القيد {self.entry_number} - {reason}",
            reference_number=self.entry_number,
            total_amount=self.total_amount,
            accounting_period=AccountingPeriod.objects.get(is_current=True),
            created_by=user
        )

        # إنشاء معاملات عكسية
        for transaction in self.transactions.all():
            Transaction.objects.create(
                journal_entry=reversed_entry,
                account=transaction.account,
                description=transaction.description,
                debit_amount=transaction.credit_amount,
                credit_amount=transaction.debit_amount
            )

        reversed_entry.post(user)

        self.is_reversed = True
        self.save()

        return reversed_entry

    def __str__(self):
        return f"قيد {self.entry_number} - {self.entry_date}"

    class Meta:
        verbose_name = 'قيد يومية'
        verbose_name_plural = 'قيود اليومية'
        ordering = ['-entry_date', '-entry_number']

class Transaction(models.Model):
    """المعاملات المحاسبية"""
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE, related_name='transactions', verbose_name='قيد اليومية')
    account = models.ForeignKey(Account, on_delete=models.PROTECT, verbose_name='الحساب')
    description = models.TextField(verbose_name='البيان')
    debit_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='مدين')
    credit_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='دائن')
    transaction_date = models.DateField(verbose_name='تاريخ المعاملة')

    def clean(self):
        # التأكد من أن المعاملة إما مدين أو دائن وليس كلاهما
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError('لا يمكن أن تكون المعاملة مدين ودائن في نفس الوقت')

        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError('يجب أن يكون للمعاملة مبلغ مدين أو دائن')

        # نسخ تاريخ القيد
        if self.journal_entry:
            self.transaction_date = self.journal_entry.entry_date

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        amount = self.debit_amount if self.debit_amount > 0 else self.credit_amount
        type_str = "مدين" if self.debit_amount > 0 else "دائن"
        return f"{self.account.name} - {amount} ({type_str})"

    class Meta:
        verbose_name = 'معاملة محاسبية'
        verbose_name_plural = 'المعاملات المحاسبية'

class PaymentMethod(models.Model):
    """طرق الدفع"""
    PAYMENT_TYPES = [
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('credit_card', 'بطاقة ائتمان'),
        ('debit_card', 'بطاقة خصم'),
        ('online', 'دفع إلكتروني'),
    ]

    name = models.CharField(max_length=100, verbose_name='اسم طريقة الدفع')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES, verbose_name='نوع الدفع')
    account = models.ForeignKey(Account, on_delete=models.PROTECT, verbose_name='الحساب المرتبط')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')

    def __str__(self):
        return f"{self.name} ({self.get_payment_type_display()})"

    class Meta:
        verbose_name = 'طريقة دفع'
        verbose_name_plural = 'طرق الدفع'

class Invoice(models.Model):
    """الفواتير"""
    INVOICE_TYPES = [
        ('sales', 'فاتورة مبيعات'),
        ('purchase', 'فاتورة مشتريات'),
        ('return_sales', 'فاتورة مرتجع مبيعات'),
        ('return_purchase', 'فاتورة مرتجع مشتريات'),
    ]

    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('partially_paid', 'مدفوعة جزئياً'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')
    invoice_type = models.CharField(max_length=20, choices=INVOICE_TYPES, verbose_name='نوع الفاتورة')
    invoice_date = models.DateField(verbose_name='تاريخ الفاتورة')
    due_date = models.DateField(verbose_name='تاريخ الاستحقاق')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='الحالة')

    # العميل أو المورد
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, blank=True, null=True, verbose_name='المورد')
    beneficiary = models.ForeignKey(Beneficiary, on_delete=models.PROTECT, blank=True, null=True, verbose_name='المستفيد')

    # المبالغ
    subtotal = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='المجموع الفرعي')
    tax_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='مبلغ الضريبة')
    discount_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='مبلغ الخصم')
    total_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='المبلغ الإجمالي')
    paid_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='المبلغ المدفوع')
    remaining_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='المبلغ المتبقي')

    # ربط بالعمليات
    incoming_operation = models.OneToOneField('IncomingOperation', on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الوارد')
    outgoing_operation = models.OneToOneField('OutgoingOperation', on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الصادر')
    incoming_return = models.OneToOneField('IncomingReturn', on_delete=models.CASCADE, blank=True, null=True, verbose_name='مرتجع الوارد')
    outgoing_return = models.OneToOneField('OutgoingReturn', on_delete=models.CASCADE, blank=True, null=True, verbose_name='مرتجع الصادر')

    # معلومات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    terms_and_conditions = models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')

    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        items = self.items.all()
        self.subtotal = sum(item.total_amount for item in items)
        self.tax_amount = sum(item.tax_amount for item in items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        self.remaining_amount = self.total_amount - self.paid_amount

        # تحديث الحالة
        if self.paid_amount == 0:
            if self.due_date < timezone.now().date():
                self.status = 'overdue'
            elif self.status not in ['draft', 'sent']:
                self.status = 'sent'
        elif self.paid_amount >= self.total_amount:
            self.status = 'paid'
        else:
            self.status = 'partially_paid'

    def save(self, *args, **kwargs):
        self.calculate_totals()
        super().save(*args, **kwargs)

    def create_journal_entry(self, user):
        """إنشاء قيد محاسبي للفاتورة"""
        if hasattr(self, 'journal_entry') and self.journal_entry:
            return self.journal_entry

        # تحديد الحسابات حسب نوع الفاتورة
        if self.invoice_type == 'sales':
            debit_account = Account.objects.get(code='1120')  # حساب العملاء
            credit_account = Account.objects.get(code='4100')  # حساب المبيعات
        elif self.invoice_type == 'purchase':
            debit_account = Account.objects.get(code='5100')  # حساب المشتريات
            credit_account = Account.objects.get(code='2120')  # حساب الموردين
        else:
            return None

        # إنشاء قيد اليومية
        entry = JournalEntry.objects.create(
            entry_number=f"INV-{self.invoice_number}",
            entry_date=self.invoice_date,
            entry_type='automatic',
            description=f"فاتورة {self.get_invoice_type_display()} رقم {self.invoice_number}",
            reference_number=self.invoice_number,
            total_amount=self.total_amount,
            accounting_period=AccountingPeriod.objects.get(is_current=True),
            created_by=user
        )

        # إنشاء المعاملات
        Transaction.objects.create(
            journal_entry=entry,
            account=debit_account,
            description=f"فاتورة {self.invoice_number}",
            debit_amount=self.total_amount,
            credit_amount=0,
            transaction_date=self.invoice_date
        )

        Transaction.objects.create(
            journal_entry=entry,
            account=credit_account,
            description=f"فاتورة {self.invoice_number}",
            debit_amount=0,
            credit_amount=self.total_amount,
            transaction_date=self.invoice_date
        )

        # ربط القيد بالفاتورة
        if self.invoice_type == 'sales' and self.outgoing_operation:
            entry.outgoing_operation = self.outgoing_operation
        elif self.invoice_type == 'purchase' and self.incoming_operation:
            entry.incoming_operation = self.incoming_operation

        entry.save()
        return entry

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.get_invoice_type_display()}"

    class Meta:
        verbose_name = 'فاتورة'
        verbose_name_plural = 'الفواتير'
        ordering = ['-invoice_date', '-invoice_number']

class InvoiceItem(models.Model):
    """بنود الفاتورة"""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items', verbose_name='الفاتورة')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية')
    unit_price = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='سعر الوحدة')
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name='نسبة الخصم %')
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=15.00, verbose_name='معدل الضريبة %')

    # المبالغ المحسوبة
    line_total = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='إجمالي السطر')
    discount_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='مبلغ الخصم')
    tax_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='مبلغ الضريبة')
    total_amount = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='المبلغ الإجمالي')

    def calculate_amounts(self):
        """حساب مبالغ البند"""
        self.line_total = self.quantity * self.unit_price
        self.discount_amount = (self.line_total * self.discount_percentage) / 100
        subtotal = self.line_total - self.discount_amount
        self.tax_amount = (subtotal * self.tax_rate) / 100
        self.total_amount = subtotal + self.tax_amount

    def save(self, *args, **kwargs):
        self.calculate_amounts()
        super().save(*args, **kwargs)
        # تحديث إجماليات الفاتورة
        self.invoice.save()

    def __str__(self):
        return f"{self.category.name} - {self.quantity} × {self.unit_price}"

    class Meta:
        verbose_name = 'بند فاتورة'
        verbose_name_plural = 'بنود الفواتير'

class Payment(models.Model):
    """المدفوعات"""
    PAYMENT_TYPES = [
        ('received', 'مقبوض'),
        ('paid', 'مدفوع'),
    ]

    payment_number = models.CharField(max_length=50, unique=True, verbose_name='رقم الدفعة')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES, verbose_name='نوع الدفعة')
    payment_date = models.DateField(verbose_name='تاريخ الدفع')
    amount = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='المبلغ')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.PROTECT, verbose_name='طريقة الدفع')

    # المرجع
    invoice = models.ForeignKey(Invoice, on_delete=models.PROTECT, blank=True, null=True, verbose_name='الفاتورة')
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, blank=True, null=True, verbose_name='المورد')
    beneficiary = models.ForeignKey(Beneficiary, on_delete=models.PROTECT, blank=True, null=True, verbose_name='المستفيد')

    # تفاصيل إضافية
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='رقم المرجع')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # تحديث المبلغ المدفوع في الفاتورة
        if self.invoice:
            total_payments = Payment.objects.filter(invoice=self.invoice).aggregate(
                total=Sum('amount')
            )['total'] or 0
            self.invoice.paid_amount = total_payments
            self.invoice.save()

    def create_journal_entry(self, user):
        """إنشاء قيد محاسبي للدفعة"""
        # تحديد الحسابات حسب نوع الدفعة
        if self.payment_type == 'received':
            debit_account = self.payment_method.account  # حساب النقدية/البنك
            if self.invoice and self.invoice.invoice_type == 'sales':
                credit_account = Account.objects.get(code='1120')  # حساب العملاء
            else:
                credit_account = Account.objects.get(code='4100')  # حساب الإيرادات
        else:  # paid
            credit_account = self.payment_method.account  # حساب النقدية/البنك
            if self.invoice and self.invoice.invoice_type == 'purchase':
                debit_account = Account.objects.get(code='2120')  # حساب الموردين
            else:
                debit_account = Account.objects.get(code='5100')  # حساب المصروفات

        # إنشاء قيد اليومية
        entry = JournalEntry.objects.create(
            entry_number=f"PAY-{self.payment_number}",
            entry_date=self.payment_date,
            entry_type='automatic',
            description=f"دفعة {self.get_payment_type_display()} رقم {self.payment_number}",
            reference_number=self.payment_number,
            total_amount=self.amount,
            accounting_period=AccountingPeriod.objects.get(is_current=True),
            created_by=user
        )

        # إنشاء المعاملات
        Transaction.objects.create(
            journal_entry=entry,
            account=debit_account,
            description=f"دفعة {self.payment_number}",
            debit_amount=self.amount,
            credit_amount=0,
            transaction_date=self.payment_date
        )

        Transaction.objects.create(
            journal_entry=entry,
            account=credit_account,
            description=f"دفعة {self.payment_number}",
            debit_amount=0,
            credit_amount=self.amount,
            transaction_date=self.payment_date
        )

        entry.save()
        return entry

    def __str__(self):
        return f"دفعة {self.payment_number} - {self.amount}"

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'المدفوعات'
        ordering = ['-payment_date']

class CashFlow(models.Model):
    """التدفق النقدي"""
    FLOW_TYPES = [
        ('inflow', 'تدفق داخل'),
        ('outflow', 'تدفق خارج'),
    ]

    CATEGORIES = [
        ('operating', 'أنشطة تشغيلية'),
        ('investing', 'أنشطة استثمارية'),
        ('financing', 'أنشطة تمويلية'),
    ]

    date = models.DateField(verbose_name='التاريخ')
    flow_type = models.CharField(max_length=20, choices=FLOW_TYPES, verbose_name='نوع التدفق')
    category = models.CharField(max_length=20, choices=CATEGORIES, verbose_name='الفئة')
    amount = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='المبلغ')
    description = models.TextField(verbose_name='الوصف')
    account = models.ForeignKey(Account, on_delete=models.PROTECT, verbose_name='الحساب')

    # ربط بالمعاملات الأصلية
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, blank=True, null=True, verbose_name='الدفعة')
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE, blank=True, null=True, verbose_name='قيد اليومية')

    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.get_flow_type_display()} - {self.amount} - {self.date}"

    class Meta:
        verbose_name = 'تدفق نقدي'
        verbose_name_plural = 'التدفقات النقدية'
        ordering = ['-date']

class Budget(models.Model):
    """الميزانيات"""
    name = models.CharField(max_length=200, verbose_name='اسم الميزانية')
    fiscal_year = models.IntegerField(verbose_name='السنة المالية')
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ النهاية')
    total_budget = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='إجمالي الميزانية')
    is_active = models.BooleanField(default=True, verbose_name='نشطة')

    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def get_actual_vs_budget(self):
        """مقارنة الفعلي مع المخطط"""
        budget_items = self.items.all()
        results = []

        for item in budget_items:
            actual = item.get_actual_amount()
            variance = actual - item.budgeted_amount
            variance_percentage = (variance / item.budgeted_amount * 100) if item.budgeted_amount > 0 else 0

            results.append({
                'account': item.account,
                'budgeted': item.budgeted_amount,
                'actual': actual,
                'variance': variance,
                'variance_percentage': variance_percentage
            })

        return results

    def __str__(self):
        return f"ميزانية {self.name} - {self.fiscal_year}"

    class Meta:
        verbose_name = 'ميزانية'
        verbose_name_plural = 'الميزانيات'
        ordering = ['-fiscal_year']

class BudgetItem(models.Model):
    """بنود الميزانية"""
    budget = models.ForeignKey(Budget, on_delete=models.CASCADE, related_name='items', verbose_name='الميزانية')
    account = models.ForeignKey(Account, on_delete=models.PROTECT, verbose_name='الحساب')
    budgeted_amount = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='المبلغ المخطط')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    def get_actual_amount(self):
        """حساب المبلغ الفعلي"""
        transactions = Transaction.objects.filter(
            account=self.account,
            transaction_date__gte=self.budget.start_date,
            transaction_date__lte=self.budget.end_date
        )

        if self.account.account_type.account_type in ['asset', 'expense', 'cost_of_goods']:
            return transactions.aggregate(total=Sum('debit_amount'))['total'] or 0
        else:
            return transactions.aggregate(total=Sum('credit_amount'))['total'] or 0

    def get_variance(self):
        """حساب الانحراف"""
        actual = self.get_actual_amount()
        return actual - self.budgeted_amount

    def get_variance_percentage(self):
        """حساب نسبة الانحراف"""
        variance = self.get_variance()
        if self.budgeted_amount > 0:
            return (variance / self.budgeted_amount) * 100
        return 0

    def __str__(self):
        return f"{self.account.name} - {self.budgeted_amount}"

    class Meta:
        verbose_name = 'بند ميزانية'
        verbose_name_plural = 'بنود الميزانيات'
        unique_together = ['budget', 'account']

# ================================
# إشارات لربط النظام المالي بالعمليات
# ================================

# سيتم إضافة الإشارات لاحقاً في ملف منفصل















