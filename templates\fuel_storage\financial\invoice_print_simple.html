<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', 'Tahoma', 'Arial Unicode MS', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            background: white;
            padding: 20px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #2c5aa0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        /* Header */
        .invoice-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Invoice Info */
        .invoice-info {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #eee;
        }
        
        .invoice-title {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .invoice-title h1 {
            font-size: 32px;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 20px;
            font-weight: 600;
            color: #e74c3c;
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            border: 2px solid #e74c3c;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 10px;
        }
        
        .status-draft { background: #ffeaa7; color: #d63031; }
        .status-sent { background: #74b9ff; color: #0984e3; }
        .status-paid { background: #00b894; color: white; }
        .status-partially-paid { background: #fdcb6e; color: #e17055; }
        .status-overdue { background: #e17055; color: white; }
        .status-cancelled { background: #636e72; color: white; }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .info-section h3 {
            font-size: 18px;
            color: #2c5aa0;
            margin-bottom: 15px;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        /* Items Table */
        .items-section {
            padding: 30px;
        }
        
        .section-title {
            font-size: 20px;
            color: #2c5aa0;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 10px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .items-table th {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
        }
        
        .items-table td {
            padding: 15px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background: #e3f2fd;
        }
        
        .description-cell {
            text-align: right !important;
            max-width: 200px;
            word-wrap: break-word;
        }
        
        .amount-cell {
            font-weight: 600;
            color: #2c5aa0;
        }
        
        /* Totals */
        .totals-section {
            padding: 0 30px 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            width: 400px;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .totals-table tr {
            border-bottom: 1px solid #eee;
        }
        
        .totals-table tr:last-child {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            color: white;
            font-weight: 700;
            font-size: 18px;
        }
        
        .totals-table td {
            padding: 15px 20px;
        }
        
        .totals-label {
            font-weight: 600;
            text-align: right;
        }
        
        .totals-value {
            text-align: left;
            font-weight: 600;
            color: #2c5aa0;
        }
        
        .totals-table tr:last-child .totals-value {
            color: white;
        }
        
        /* Amount in Words */
        .amount-words {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 30px;
            border-right: 5px solid #2c5aa0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .amount-words strong {
            color: #2c5aa0;
            font-size: 18px;
        }
        
        /* Footer */
        .invoice-footer {
            padding: 30px;
            background: #f8f9fa;
            border-top: 2px solid #eee;
        }
        
        .terms {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }
        
        .terms h4 {
            color: #2c5aa0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .terms p {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin-top: 40px;
        }
        
        .signature-box {
            text-align: center;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-bottom: 15px;
            height: 60px;
            position: relative;
        }
        
        .signature-label {
            font-weight: 600;
            color: #555;
            font-size: 16px;
        }
        
        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
                padding: 0;
            }
            
            .invoice-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
            }
            
            .no-print {
                display: none !important;
            }
            
            .items-table, .totals-table {
                box-shadow: none;
            }
            
            .invoice-header {
                background: #2c5aa0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .items-table th {
                background: #2c5aa0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .totals-table tr:last-child {
                background: #2c5aa0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        /* Print Button */
        .print-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #1e3d72;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .print-button i {
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i>
        طباعة الفاتورة
    </button>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-name">{{ company_name }}</div>
            <div class="company-details">
                {{ company_address }}<br>
                هاتف: {{ company_phone }} • البريد الإلكتروني: {{ company_email }}
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <div class="invoice-title">
                <h1>فاتورة</h1>
                <div class="invoice-number">{{ invoice.invoice_number }}</div>
                <div class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </div>
            </div>

            <div class="info-grid">
                <div class="info-section">
                    <h3>معلومات الفاتورة</h3>
                    <div class="info-item">
                        <span class="info-label">نوع الفاتورة:</span>
                        <span class="info-value">{{ invoice.get_invoice_type_display }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الفاتورة:</span>
                        <span class="info-value">{{ invoice.invoice_date|date:"j F Y" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الاستحقاق:</span>
                        <span class="info-value">{{ invoice.due_date|date:"j F Y" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">أنشئت بواسطة:</span>
                        <span class="info-value">{{ invoice.created_by.full_name }}</span>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3>
                        {% if invoice.invoice_type == 'purchase' %}
                            معلومات المورد
                        {% else %}
                            معلومات العميل
                        {% endif %}
                    </h3>
                    {% if invoice.supplier %}
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{{ invoice.supplier.full_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ invoice.supplier.phone|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد:</span>
                            <span class="info-value">{{ invoice.supplier.email|default:"غير محدد" }}</span>
                        </div>
                    {% elif invoice.beneficiary %}
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{{ invoice.beneficiary.full_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ invoice.beneficiary.phone|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد:</span>
                            <span class="info-value">{{ invoice.beneficiary.email|default:"غير محدد" }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Items Section -->
        <div class="items-section">
            <div class="section-title">بنود الفاتورة</div>
            
            {% if items %}
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 20%;">الصنف</th>
                        <th style="width: 30%;">الوصف</th>
                        <th style="width: 10%;">الكمية</th>
                        <th style="width: 12%;">سعر الوحدة</th>
                        <th style="width: 8%;">الخصم %</th>
                        <th style="width: 15%;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.category.name }}</td>
                        <td class="description-cell">{{ item.description|default:"بدون وصف" }}</td>
                        <td>{{ item.quantity|floatformat:2 }}</td>
                        <td>{{ item.unit_price|floatformat:2 }}</td>
                        <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                        <td class="amount-cell">{{ item.total_amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div style="text-align: center; padding: 40px; color: #666;">
                لا توجد بنود في هذه الفاتورة
            </div>
            {% endif %}
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="totals-label">المجموع الفرعي:</td>
                    <td class="totals-value">{{ invoice.subtotal|floatformat:2 }} ريال</td>
                </tr>
                {% if invoice.discount_amount > 0 %}
                <tr>
                    <td class="totals-label">الخصم:</td>
                    <td class="totals-value">{{ invoice.discount_amount|floatformat:2 }} ريال</td>
                </tr>
                {% endif %}
                {% if invoice.tax_amount > 0 %}
                <tr>
                    <td class="totals-label">الضريبة:</td>
                    <td class="totals-value">{{ invoice.tax_amount|floatformat:2 }} ريال</td>
                </tr>
                {% endif %}
                <tr>
                    <td class="totals-label">الإجمالي:</td>
                    <td class="totals-value">{{ invoice.total_amount|floatformat:2 }} ريال</td>
                </tr>
            </table>
        </div>

        <!-- Amount in Words -->
        {% load financial_filters %}
        <div class="amount-words">
            <strong>المبلغ بالكلمات:</strong><br>
            {{ invoice.total_amount|to_words }}
        </div>

        <!-- Footer -->
        <div class="invoice-footer">
            {% if invoice.terms_and_conditions %}
            <div class="terms">
                <h4>الشروط والأحكام:</h4>
                <p>{{ invoice.terms_and_conditions }}</p>
            </div>
            {% endif %}
            
            {% if invoice.notes %}
            <div class="terms">
                <h4>ملاحظات:</h4>
                <p>{{ invoice.notes }}</p>
            </div>
            {% endif %}
            
            <!-- Signatures -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">توقيع المورد</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">توقيع المستلم</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printInvoice() {
            window.print();
        }
        
        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
