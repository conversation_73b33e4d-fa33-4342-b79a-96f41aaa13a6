from django.http import HttpResponse
from django.template.loader import get_template
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import BarChart, Reference
import io
from datetime import datetime
from decimal import Decimal

def generate_pdf_report(report_data, report_type):
    """إنشاء تقرير PDF محسن"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # إعداد الخطوط العربية
    try:
        pdfmetrics.registerFont(TTFont('Arabic', 'static/fonts/NotoSansArabic-Regular.ttf'))
        arabic_style = ParagraphStyle('Arabic', fontName='Arabic', fontSize=12, alignment=2)
        title_style = ParagraphStyle('ArabicTitle', fontName='Arabic', fontSize=16, alignment=1, spaceAfter=20)
        header_style = ParagraphStyle('ArabicHeader', fontName='Arabic', fontSize=14, alignment=2, spaceAfter=10)
    except:
        arabic_style = ParagraphStyle('Arabic', fontName='Helvetica', fontSize=12, alignment=2)
        title_style = ParagraphStyle('ArabicTitle', fontName='Helvetica-Bold', fontSize=16, alignment=1, spaceAfter=20)
        header_style = ParagraphStyle('ArabicHeader', fontName='Helvetica-Bold', fontSize=14, alignment=2, spaceAfter=10)
    
    story = []
    
    # العنوان الرئيسي
    title = Paragraph(report_data['title'], title_style)
    story.append(title)
    story.append(Spacer(1, 12))
    
    # معلومات التقرير
    info_data = [
        ['تاريخ الإنشاء:', datetime.now().strftime('%Y-%m-%d %H:%M')],
        ['المستخدم:', report_data.get('user', 'النظام')],
    ]
    
    if report_data.get('from_date') and report_data.get('to_date'):
        info_data.append(['الفترة:', f"من {report_data['from_date']} إلى {report_data['to_date']}"])
    
    info_table = Table(info_data, colWidths=[2*inch, 4*inch])
    info_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    story.append(info_table)
    story.append(Spacer(1, 20))
    
    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'financial_summary':
        story.extend(_create_financial_summary_content(report_data, arabic_style, header_style))
    elif report_type == 'inventory_valuation':
        story.extend(_create_inventory_valuation_content(report_data, arabic_style, header_style))
    elif report_type == 'profit_loss':
        story.extend(_create_profit_loss_content(report_data, arabic_style, header_style))
    elif report_type == 'storage_movement':
        story.extend(_create_storage_movement_content(report_data, arabic_style, header_style))
    elif report_type == 'storage_status':
        story.extend(_create_storage_status_content(report_data, arabic_style, header_style))
    
    # إنشاء PDF
    doc.build(story)
    buffer.seek(0)
    
    response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
    
    return response

def generate_excel_report(report_data, report_type):
    """إنشاء تقرير Excel محسن"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "التقرير"
    
    # تنسيق الخلايا
    header_font = Font(bold=True, size=14, color="FFFFFF")
    title_font = Font(bold=True, size=16)
    normal_font = Font(size=12)
    number_font = Font(size=12, name='Courier New')
    
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    light_fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # العنوان
    ws['A1'] = report_data['title']
    ws['A1'].font = title_font
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')
    
    # معلومات التقرير
    row = 3
    ws[f'A{row}'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
    ws[f'A{row}'].font = normal_font
    row += 1
    
    if report_data.get('from_date') and report_data.get('to_date'):
        ws[f'A{row}'] = f"الفترة: من {report_data['from_date']} إلى {report_data['to_date']}"
        ws[f'A{row}'].font = normal_font
        row += 2
    
    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'financial_summary':
        _create_excel_financial_summary(ws, report_data, row, header_font, normal_font, number_font, header_fill, thin_border)
    elif report_type == 'inventory_valuation':
        _create_excel_inventory_valuation(ws, report_data, row, header_font, normal_font, number_font, header_fill, thin_border)
    elif report_type == 'profit_loss':
        _create_excel_profit_loss(ws, report_data, row, header_font, normal_font, number_font, header_fill, thin_border)
    elif report_type == 'storage_movement':
        _create_excel_storage_movement(ws, report_data, row, header_font, normal_font, number_font, header_fill, thin_border)
    elif report_type == 'storage_status':
        _create_excel_storage_status(ws, report_data, row, header_font, normal_font, number_font, header_fill, thin_border)
    
    # تنسيق عام للجدول
    for row in ws.iter_rows():
        for cell in row:
            if cell.value:
                cell.border = thin_border
    
    # ضبط عرض الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # حفظ الملف
    buffer = io.BytesIO()
    wb.save(buffer)
    buffer.seek(0)
    
    response = HttpResponse(
        buffer.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
    
    return response

# دوال مساعدة لإنشاء محتوى PDF
def _create_financial_summary_content(report_data, style, header_style):
    """إنشاء محتوى الملخص المالي"""
    content = []
    
    content.append(Paragraph("الملخص المالي", header_style))
    content.append(Spacer(1, 12))
    
    # إجماليات مالية
    financial_data = [
        ['البيان', 'المبلغ (ريال سعودي)'],
        ['إجمالي المشتريات', f"{report_data.get('total_purchases', 0):,.2f}"],
        ['إجمالي المبيعات', f"{report_data.get('total_sales', 0):,.2f}"],
        ['إجمالي الأرباح', f"{report_data.get('total_profit', 0):,.2f}"],
        ['قيمة المخزون الحالي', f"{report_data.get('inventory_value', 0):,.2f}"],
    ]
    
    table = Table(financial_data, colWidths=[3*inch, 2*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
    ]))
    content.append(table)
    
    return content

def _create_inventory_valuation_content(report_data, style, header_style):
    """إنشاء محتوى تقييم المخزون"""
    content = []
    
    content.append(Paragraph("تقييم المخزون", header_style))
    content.append(Spacer(1, 12))
    
    if report_data.get('inventory_items'):
        data = [['الصنف', 'الكمية', 'متوسط التكلفة', 'القيمة الإجمالية']]
        total_value = 0
        
        for item in report_data['inventory_items']:
            value = item['quantity'] * item['avg_cost']
            total_value += value
            data.append([
                item['category'],
                f"{item['quantity']:,.3f}",
                f"{item['avg_cost']:,.3f}",
                f"{value:,.2f}"
            ])
        
        data.append(['الإجمالي', '', '', f"{total_value:,.2f}"])
        
        table = Table(data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), colors.beige),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
        ]))
        content.append(table)
    
    return content

def _create_profit_loss_content(report_data, style, header_style):
    """إنشاء محتوى قائمة الأرباح والخسائر"""
    content = []
    
    content.append(Paragraph("قائمة الأرباح والخسائر", header_style))
    content.append(Spacer(1, 12))
    
    # بيانات الأرباح والخسائر
    pl_data = [
        ['البيان', 'المبلغ (ريال سعودي)'],
        ['الإيرادات', ''],
        ['  إجمالي المبيعات', f"{report_data.get('total_sales', 0):,.2f}"],
        ['  إيرادات أخرى', f"{report_data.get('other_income', 0):,.2f}"],
        ['إجمالي الإيرادات', f"{report_data.get('total_revenue', 0):,.2f}"],
        ['', ''],
        ['التكاليف', ''],
        ['  تكلفة البضاعة المباعة', f"{report_data.get('cost_of_goods', 0):,.2f}"],
        ['  مصروفات التشغيل', f"{report_data.get('operating_expenses', 0):,.2f}"],
        ['  خسائر التلف', f"{report_data.get('damage_losses', 0):,.2f}"],
        ['إجمالي التكاليف', f"{report_data.get('total_costs', 0):,.2f}"],
        ['', ''],
        ['صافي الربح/الخسارة', f"{report_data.get('net_profit', 0):,.2f}"],
    ]
    
    table = Table(pl_data, colWidths=[3*inch, 2*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        # تمييز الإجماليات
        ('BACKGROUND', (0, 4), (-1, 4), colors.lightgrey),
        ('BACKGROUND', (0, 10), (-1, 10), colors.lightgrey),
        ('BACKGROUND', (0, -1), (-1, -1), colors.yellow),
        ('FONTNAME', (0, 4), (-1, 4), 'Helvetica-Bold'),
        ('FONTNAME', (0, 10), (-1, 10), 'Helvetica-Bold'),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
    ]))
    content.append(table)
    
    return content

def _create_storage_movement_content(report_data, style, header_style):
    """إنشاء محتوى حركة المخزن"""
    content = []
    
    # عمليات الوارد
    if report_data['data'].get('incoming_operations'):
        content.append(Paragraph("عمليات الوارد", header_style))
        content.append(Spacer(1, 6))
        
        data = [['الرقم الورقي', 'المورد', 'التاريخ', 'القيمة الإجمالية']]
        total_incoming = 0
        
        for op in report_data['data']['incoming_operations']:
            total_incoming += float(op.total_amount or 0)
            data.append([
                op.paper_number,
                op.supplier.full_name,
                op.operation_date.strftime('%Y-%m-%d'),
                f"{op.total_amount:,.2f}"
            ])
        
        data.append(['الإجمالي', '', '', f"{total_incoming:,.2f}"])
        
        table = Table(data, colWidths=[1.5*inch, 2*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.green),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), colors.lightgreen),
            ('BACKGROUND', (0, -1), (-1, -1), colors.green),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (3, 1), (3, -1), 'RIGHT'),
        ]))
        content.append(table)
        content.append(Spacer(1, 12))
    
    # عمليات الصادر
    if report_data['data'].get('outgoing_operations'):
        content.append(Paragraph("عمليات الصادر", header_style))
        content.append(Spacer(1, 6))
        
        data = [['الرقم الورقي', 'المستفيد', 'التاريخ', 'القيمة الإجمالية']]
        total_outgoing = 0
        
        for op in report_data['data']['outgoing_operations']:
            total_outgoing += float(op.total_amount or 0)
            data.append([
                op.paper_number,
                op.beneficiary.full_name,
                op.operation_date.strftime('%Y-%m-%d'),
                f"{op.total_amount:,.2f}"
            ])
        
        data.append(['الإجمالي', '', '', f"{total_outgoing:,.2f}"])
        
        table = Table(data, colWidths=[1.5*inch, 2*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.red),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), colors.mistyrose),
            ('BACKGROUND', (0, -1), (-1, -1), colors.red),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (3, 1), (3, -1), 'RIGHT'),
        ]))
        content.append(table)
    
    return content

def _create_storage_status_content(report_data, style, header_style):
    """إنشاء محتوى حالة المخزن"""
    content = []
    
    if report_data.get('items'):
        content.append(Paragraph("أصناف المخزن", header_style))
        content.append(Spacer(1, 6))
        
        data = [['الصنف', 'وحدة القياس', 'الكمية الحالية', 'متوسط التكلفة', 'القيمة الإجمالية']]
        total_value = 0
        
        for item in report_data['items']:
            value = float(item.current_quantity) * float(item.average_cost)
            total_value += value
            data.append([
                item.category.name,
                item.get_unit_of_measure_display(),
                f"{item.current_quantity:,.3f}",
                f"{item.average_cost:,.3f}",
                f"{value:,.2f}"
            ])
        
        data.append(['الإجمالي', '', '', '', f"{total_value:,.2f}"])
        
        table = Table(data, colWidths=[1.5*inch, 1*inch, 1.2*inch, 1.2*inch, 1.2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.blue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), colors.lightblue),
            ('BACKGROUND', (0, -1), (-1, -1), colors.blue),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),
        ]))
        content.append(table)
    
    return content

# دوال مساعدة لإنشاء محتوى Excel
def _create_excel_financial_summary(ws, report_data, start_row, header_font, normal_font, number_font, header_fill, border):
    """إنشاء ملخص مالي في Excel"""
    row = start_row
    
    # عنوان القسم
    ws[f'A{row}'] = "الملخص المالي"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:B{row}')
    row += 2
    
    # البيانات المالية
    financial_data = [
        ('إجمالي المشتريات', report_data.get('total_purchases', 0)),
        ('إجمالي المبيعات', report_data.get('total_sales', 0)),
        ('إجمالي الأرباح', report_data.get('total_profit', 0)),
        ('قيمة المخزون الحالي', report_data.get('inventory_value', 0)),
    ]
    
    for label, value in financial_data:
        ws[f'A{row}'] = label
        ws[f'A{row}'].font = normal_font
        ws[f'B{row}'] = float(value)
        ws[f'B{row}'].font = number_font
        ws[f'B{row}'].number_format = '#,##0.00'
        row += 1

def _create_excel_inventory_valuation(ws, report_data, start_row, header_font, normal_font, number_font, header_fill, border):
    """إنشاء تقييم مخزون في Excel"""
    row = start_row
    
    # عنوان القسم
    ws[f'A{row}'] = "تقييم المخزون"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:D{row}')
    row += 2
    
    # رؤوس الأعمدة
    headers = ['الصنف', 'الكمية', 'متوسط التكلفة', 'القيمة الإجمالية']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
    
    row += 1
    
    # البيانات
    if report_data.get('inventory_items'):
        total_value = 0
        for item in report_data['inventory_items']:
            value = item['quantity'] * item['avg_cost']
            total_value += value
            
            ws.cell(row=row, column=1, value=item['category']).font = normal_font
            ws.cell(row=row, column=2, value=float(item['quantity'])).font = number_font
            ws.cell(row=row, column=3, value=float(item['avg_cost'])).font = number_font
            ws.cell(row=row, column=4, value=float(value)).font = number_font
            
            # تنسيق الأرقام
            ws.cell(row=row, column=2).number_format = '#,##0.000'
            ws.cell(row=row, column=3).number_format = '#,##0.000'
            ws.cell(row=row, column=4).number_format = '#,##0.00'
            
            row += 1
        
        # الإجمالي
        ws.cell(row=row, column=1, value='الإجمالي').font = header_font
        ws.cell(row=row, column=4, value=float(total_value)).font = header_font
        ws.cell(row=row, column=4).number_format = '#,##0.00'

def _create_excel_profit_loss(ws, report_data, start_row, header_font, normal_font, number_font, header_fill, border):
    """إنشاء قائمة أرباح وخسائر في Excel"""
    row = start_row
    
    # عنوان القسم
    ws[f'A{row}'] = "قائمة الأرباح والخسائر"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:B{row}')
    row += 2
    
    # بيانات الأرباح والخسائر
    pl_data = [
        ('الإيرادات', ''),
        ('  إجمالي المبيعات', report_data.get('total_sales', 0)),
        ('  إيرادات أخرى', report_data.get('other_income', 0)),
        ('إجمالي الإيرادات', report_data.get('total_revenue', 0)),
        ('', ''),
        ('التكاليف', ''),
        ('  تكلفة البضاعة المباعة', report_data.get('cost_of_goods', 0)),
        ('  مصروفات التشغيل', report_data.get('operating_expenses', 0)),
        ('  خسائر التلف', report_data.get('damage_losses', 0)),
        ('إجمالي التكاليف', report_data.get('total_costs', 0)),
        ('', ''),
        ('صافي الربح/الخسارة', report_data.get('net_profit', 0)),
    ]
    
    for label, value in pl_data:
        ws[f'A{row}'] = label
        if label.startswith('إجمالي') or label.startswith('صافي'):
            ws[f'A{row}'].font = header_font
        else:
            ws[f'A{row}'].font = normal_font
        
        if value != '':
            ws[f'B{row}'] = float(value)
            ws[f'B{row}'].font = number_font if not label.startswith('إجمالي') and not label.startswith('صافي') else header_font
            ws[f'B{row}'].number_format = '#,##0.00'
        
        row += 1

def _create_excel_storage_movement(ws, report_data, start_row, header_font, normal_font, number_font, header_fill, border):
    """إنشاء حركة مخزن في Excel"""
    row = start_row
    
    # عمليات الوارد
    if report_data['data'].get('incoming_operations'):
        ws[f'A{row}'] = "عمليات الوارد"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        ws.merge_cells(f'A{row}:D{row}')
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الرقم الورقي', 'المورد', 'التاريخ', 'القيمة الإجمالية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        total_incoming = 0
        for op in report_data['data']['incoming_operations']:
            total_incoming += float(op.total_amount or 0)
            ws.cell(row=row, column=1, value=op.paper_number).font = normal_font
            ws.cell(row=row, column=2, value=op.supplier.full_name).font = normal_font
            ws.cell(row=row, column=3, value=op.operation_date.strftime('%Y-%m-%d')).font = normal_font
            ws.cell(row=row, column=4, value=float(op.total_amount or 0)).font = number_font
            ws.cell(row=row, column=4).number_format = '#,##0.00'
            row += 1
        
        # الإجمالي
        ws.cell(row=row, column=1, value='الإجمالي').font = header_font
        ws.cell(row=row, column=4, value=float(total_incoming)).font = header_font
        ws.cell(row=row, column=4).number_format = '#,##0.00'
        row += 3
    
    # عمليات الصادر
    if report_data['data'].get('outgoing_operations'):
        ws[f'A{row}'] = "عمليات الصادر"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        ws.merge_cells(f'A{row}:D{row}')
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الرقم الورقي', 'المستفيد', 'التاريخ', 'القيمة الإجمالية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        total_outgoing = 0
        for op in report_data['data']['outgoing_operations']:
            total_outgoing += float(op.total_amount or 0)
            ws.cell(row=row, column=1, value=op.paper_number).font = normal_font
            ws.cell(row=row, column=2, value=op.beneficiary.full_name).font = normal_font
            ws.cell(row=row, column=3, value=op.operation_date.strftime('%Y-%m-%d')).font = normal_font
            ws.cell(row=row, column=4, value=float(op.total_amount or 0)).font = number_font
            ws.cell(row=row, column=4).number_format = '#,##0.00'
            row += 1
        
        # الإجمالي
        ws.cell(row=row, column=1, value='الإجمالي').font = header_font
        ws.cell(row=row, column=4, value=float(total_outgoing)).font = header_font
        ws.cell(row=row, column=4).number_format = '#,##0.00'

def _create_excel_storage_status(ws, report_data, start_row, header_font, normal_font, number_font, header_fill, border):
    """إنشاء حالة مخزن في Excel"""
    row = start_row
    
    if report_data.get('items'):
        ws[f'A{row}'] = "أصناف المخزن"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        ws.merge_cells(f'A{row}:E{row}')
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الصنف', 'وحدة القياس', 'الكمية الحالية', 'متوسط التكلفة', 'القيمة الإجمالية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        total_value = 0
        for item in report_data['items']:
            value = float(item.current_quantity) * float(item.average_cost)
            total_value += value
            
            ws.cell(row=row, column=1, value=item.category.name).font = normal_font
            ws.cell(row=row, column=2, value=item.get_unit_of_measure_display()).font = normal_font
            ws.cell(row=row, column=3, value=float(item.current_quantity)).font = number_font
            ws.cell(row=row, column=4, value=float(item.average_cost)).font = number_font
            ws.cell(row=row, column=5, value=float(value)).font = number_font
            
            # تنسيق الأرقام
            ws.cell(row=row, column=3).number_format = '#,##0.000'
            ws.cell(row=row, column=4).number_format = '#,##0.000'
            ws.cell(row=row, column=5).number_format = '#,##0.00'
            
            row += 1
        
        # الإجمالي
        ws.cell(row=row, column=1, value='الإجمالي').font = header_font
        ws.cell(row=row, column=5, value=float(total_value)).font = header_font
        ws.cell(row=row, column=5).number_format = '#,##0.00'

def calculate_inventory_value(storage=None, category=None):
    """حساب قيمة المخزون"""
    from .models import StorageItem
    
    queryset = StorageItem.objects.all()
    
    if storage:
        queryset = queryset.filter(storage=storage)
    if category:
        queryset = queryset.filter(category=category)
    
    total_value = 0
    for item in queryset:
        total_value += item.current_quantity * item.average_cost
    
    return total_value

def calculate_profit_loss(from_date=None, to_date=None):
    """حساب الأرباح والخسائر"""
    from .models import OutgoingOperationItem, IncomingOperationItem, DamageOperationItem
    from django.db.models import Sum
    
    # الإيرادات (المبيعات)
    sales_query = OutgoingOperationItem.objects.all()
    if from_date:
        sales_query = sales_query.filter(outgoing_operation__operation_date__gte=from_date)
    if to_date:
        sales_query = sales_query.filter(outgoing_operation__operation_date__lte=to_date)
    
    total_sales = sales_query.aggregate(total=Sum('total_price'))['total'] or 0
    
    # تكلفة البضاعة المباعة
    cost_of_goods = 0
    for item in sales_query:
        cost_of_goods += item.exported_quantity * item.cost_price
    
    # خسائر التلف
    damage_query = DamageOperationItem.objects.all()
    if from_date:
        damage_query = damage_query.filter(damage_operation__damage_date__gte=from_date)
    if to_date:
        damage_query = damage_query.filter(damage_operation__damage_date__lte=to_date)
    
    damage_losses = damage_query.aggregate(total=Sum('total_loss'))['total'] or 0
    
    # صافي الربح
    net_profit = total_sales - cost_of_goods - damage_losses
    
    return {
        'total_sales': total_sales,
        'cost_of_goods': cost_of_goods,
        'damage_losses': damage_losses,
        'net_profit': net_profit,
        'total_revenue': total_sales,
        'total_costs': cost_of_goods + damage_losses,
        'other_income': 0,  # يمكن إضافة منطق للإيرادات الأخرى
        'operating_expenses': 0,  # يمكن إضافة منطق للمصروفات التشغيلية
    }

def number_to_arabic_words(number):
    """تحويل الرقم إلى كلمات عربية"""

    # قوائم الأرقام العربية
    ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة']
    tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون']
    teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
             'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر']
    hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة',
                'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة']

    def convert_hundreds(n):
        """تحويل الأرقام من 0 إلى 999"""
        if n == 0:
            return ''

        result = []

        # المئات
        if n >= 100:
            h = n // 100
            result.append(hundreds[h])
            n %= 100

        # العشرات والآحاد
        if n >= 20:
            t = n // 10
            result.append(tens[t])
            n %= 10
            if n > 0:
                result.append(ones[n])
        elif n >= 10:
            result.append(teens[n - 10])
        elif n > 0:
            result.append(ones[n])

        return ' '.join(result)

    def convert_number(num):
        """تحويل الرقم الكامل"""
        if num == 0:
            return 'صفر'

        # فصل الجزء الصحيح والعشري
        integer_part = int(num)
        decimal_part = round((num - integer_part) * 100)

        result = []

        # تحويل الجزء الصحيح
        if integer_part >= 1000000:
            millions = integer_part // 1000000
            result.append(convert_hundreds(millions))
            if millions == 1:
                result.append('مليون')
            elif millions == 2:
                result.append('مليونان')
            elif millions <= 10:
                result.append('ملايين')
            else:
                result.append('مليون')
            integer_part %= 1000000

        if integer_part >= 1000:
            thousands = integer_part // 1000
            result.append(convert_hundreds(thousands))
            if thousands == 1:
                result.append('ألف')
            elif thousands == 2:
                result.append('ألفان')
            elif thousands <= 10:
                result.append('آلاف')
            else:
                result.append('ألف')
            integer_part %= 1000

        if integer_part > 0:
            result.append(convert_hundreds(integer_part))

        # إضافة العملة
        integer_text = ' '.join(filter(None, result))
        if integer_text:
            integer_text += ' ريال'

        # تحويل الجزء العشري (الهللات)
        if decimal_part > 0:
            decimal_text = convert_hundreds(decimal_part)
            if decimal_text:
                if integer_text:
                    return f"{integer_text} و {decimal_text} هللة"
                else:
                    return f"{decimal_text} هللة"

        return integer_text if integer_text else 'صفر ريال'

    try:
        return convert_number(float(number))
    except (ValueError, TypeError):
        return 'رقم غير صحيح'
