{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة مخازن المحروقات{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        border-radius: 0 0 20px 20px;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-color);
    }

    .stats-card.primary::before { background: linear-gradient(45deg, #667eea, #764ba2); }
    .stats-card.success::before { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
    .stats-card.info::before { background: linear-gradient(45deg, #3498db, #2980b9); }
    .stats-card.warning::before { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .stats-card.danger::before { background: linear-gradient(45deg, #e74c3c, #c0392b); }
    .stats-card.secondary::before { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-icon.primary { background: linear-gradient(45deg, #667eea, #764ba2); }
    .stats-icon.success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
    .stats-icon.info { background: linear-gradient(45deg, #3498db, #2980b9); }
    .stats-icon.warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .stats-icon.danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
    .stats-icon.secondary { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0.5rem 0;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .activity-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .activity-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .activity-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .activity-table {
        margin: 0;
    }

    .activity-table th {
        background: #f8f9fa;
        border: none;
        color: #6c757d;
        font-weight: 600;
        padding: 1rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .activity-table td {
        padding: 1rem;
        border-top: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .activity-table tbody tr:hover {
        background: #f8f9fa;
    }

    .welcome-text {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
        .dashboard-header {
            margin: -1rem -1rem 1.5rem -1rem;
            padding: 1.5rem 0;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stats-number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header text-center">
    <div class="container">
        <h1 class="dashboard-title">مرحباً بك في لوحة التحكم</h1>
        <p class="welcome-text">نظام إدارة مخازن المحروقات المتطور</p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-5">
    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card primary h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon primary mx-auto">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="stats-number">{{ total_storages }}</div>
                <div class="stats-label">إجمالي المخازن</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card success h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon success mx-auto">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stats-number">{{ total_categories }}</div>
                <div class="stats-label">إجمالي الأصناف</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card info h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon info mx-auto">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stats-number">{{ total_suppliers }}</div>
                <div class="stats-label">الموردون</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card secondary h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon secondary mx-auto">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number">{{ total_beneficiaries }}</div>
                <div class="stats-label">المستفيدون</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card warning h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon warning mx-auto">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stats-number">{{ total_incoming_operations }}</div>
                <div class="stats-label">عمليات الوارد</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card stats-card danger h-100">
            <div class="card-body text-center p-4">
                <div class="stats-icon danger mx-auto">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stats-number">{{ total_outgoing_operations }}</div>
                <div class="stats-label">عمليات الصادر</div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="activity-header">
                <h5 class="activity-title">
                    <i class="fas fa-arrow-down text-success"></i>
                    أحدث عمليات الوارد
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table activity-table">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_incoming %}
                                <tr>
                                    <td>
                                        <span class="badge bg-success">{{ operation.paper_number }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-warehouse text-muted me-2"></i>
                                        {{ operation.storage.name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-truck text-muted me-2"></i>
                                        {{ operation.supplier.full_name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-calendar text-muted me-2"></i>
                                        {{ operation.operation_date|date:"Y-m-d" }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عمليات وارد حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="activity-header">
                <h5 class="activity-title">
                    <i class="fas fa-arrow-up text-danger"></i>
                    أحدث عمليات الصادر
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table activity-table">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المستفيد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_outgoing %}
                                <tr>
                                    <td>
                                        <span class="badge bg-danger">{{ operation.paper_number }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-warehouse text-muted me-2"></i>
                                        {{ operation.storage.name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-users text-muted me-2"></i>
                                        {{ operation.beneficiary.full_name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-calendar text-muted me-2"></i>
                                        {{ operation.operation_date|date:"Y-m-d" }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عمليات صادر حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
