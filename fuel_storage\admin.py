from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary, 
    Storage, StorageItem, MaintenanceSchedule, QualityControl, SystemLog,
    IncomingOperation, OutgoingOperation, DamageOperation, StorageTransfer,
    IncomingOperationItem, OutgoingOperationItem, DamageOperationItem, StorageTransferItem,
    IncomingReturn, OutgoingReturn, IncomingReturnItem, OutgoingReturnItem,
    OperationModification
)

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'full_name', 'user_type', 'is_active')
    list_filter = ('user_type', 'is_active')
    search_fields = ('username', 'full_name')
    
    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {'fields': ('full_name', 'user_type')}),
    )

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name',)
    list_per_page = 20

@admin.register(Station)
class StationAdmin(admin.ModelAdmin):
    list_display = ('name', 'address', 'created_at')
    search_fields = ('name', 'address')
    list_per_page = 20

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Beneficiary)
class BeneficiaryAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Storage)
class StorageAdmin(admin.ModelAdmin):
    list_display = ('name', 'classification', 'keeper_name', 'phone_number', 'created_at')
    list_filter = ('classification',)
    search_fields = ('name', 'keeper_name')
    list_per_page = 20

@admin.register(StorageItem)
class StorageItemAdmin(admin.ModelAdmin):
    list_display = ('storage', 'category', 'unit_of_measure', 'opening_balance', 'current_quantity', 'updated_at')
    list_filter = ('storage', 'category', 'unit_of_measure')
    search_fields = ('storage__name', 'category__name')
    list_per_page = 20

@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    list_display = ('storage', 'maintenance_type', 'scheduled_date', 'status', 'technician_name')
    list_filter = ('maintenance_type', 'status', 'scheduled_date')
    search_fields = ('storage__name', 'description')
    list_per_page = 20

@admin.register(QualityControl)
class QualityControlAdmin(admin.ModelAdmin):
    list_display = ('storage_item', 'inspection_type', 'inspection_date', 'quality_status', 'inspector_name')
    list_filter = ('inspection_type', 'quality_status', 'inspection_date')
    search_fields = ('storage_item__category__name', 'inspector_name')
    list_per_page = 20

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'model_name', 'object_id', 'timestamp')
    list_filter = ('action', 'model_name', 'timestamp')
    search_fields = ('user__username', 'description')
    list_per_page = 20
    readonly_fields = ('timestamp',)

# Inline classes for operation items
class IncomingOperationItemInline(admin.TabularInline):
    model = IncomingOperationItem
    extra = 1

class OutgoingOperationItemInline(admin.TabularInline):
    model = OutgoingOperationItem
    extra = 1

class DamageOperationItemInline(admin.TabularInline):
    model = DamageOperationItem
    extra = 1

class StorageTransferItemInline(admin.TabularInline):
    model = StorageTransferItem
    extra = 1

class IncomingReturnItemInline(admin.TabularInline):
    model = IncomingReturnItem
    extra = 1

class OutgoingReturnItemInline(admin.TabularInline):
    model = OutgoingReturnItem
    extra = 1

# Operation Admin Classes
@admin.register(IncomingOperation)
class IncomingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'supplier', 'station', 'operation_date', 'is_locked')
    list_filter = ('storage', 'supplier', 'station', 'is_locked', 'operation_date')
    search_fields = ('paper_number', 'storage__name', 'supplier__full_name')
    inlines = [IncomingOperationItemInline]
    list_per_page = 20

@admin.register(OutgoingOperation)
class OutgoingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'beneficiary', 'operation_date', 'is_locked')
    list_filter = ('storage', 'beneficiary', 'is_locked', 'operation_date')
    search_fields = ('paper_number', 'storage__name', 'beneficiary__full_name')
    inlines = [OutgoingOperationItemInline]
    list_per_page = 20

@admin.register(DamageOperation)
class DamageOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'damage_date', 'is_locked')
    list_filter = ('storage', 'is_locked', 'damage_date')
    search_fields = ('paper_number', 'storage__name')
    inlines = [DamageOperationItemInline]
    list_per_page = 20

@admin.register(StorageTransfer)
class StorageTransferAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'from_storage', 'to_storage', 'transfer_date', 'is_locked')
    list_filter = ('from_storage', 'to_storage', 'is_locked', 'transfer_date')
    search_fields = ('paper_number', 'from_storage__name', 'to_storage__name')
    inlines = [StorageTransferItemInline]
    list_per_page = 20

@admin.register(IncomingReturn)
class IncomingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'incoming_operation', 'return_date', 'is_locked')
    list_filter = ('incoming_operation__storage', 'is_locked', 'return_date')
    search_fields = ('paper_number', 'incoming_operation__paper_number')
    inlines = [IncomingReturnItemInline]
    list_per_page = 20

@admin.register(OutgoingReturn)
class OutgoingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'outgoing_operation', 'return_date', 'is_locked')
    list_filter = ('outgoing_operation__storage', 'is_locked', 'return_date')
    search_fields = ('paper_number', 'outgoing_operation__paper_number')
    inlines = [OutgoingReturnItemInline]
    list_per_page = 20

@admin.register(OperationModification)
class OperationModificationAdmin(admin.ModelAdmin):
    list_display = ('operation_type', 'storage', 'category', 'modification_date', 'modified_by')
    list_filter = ('operation_type', 'storage', 'modification_date')
    search_fields = ('storage__name', 'category__name', 'modified_by__username')
    list_per_page = 20

