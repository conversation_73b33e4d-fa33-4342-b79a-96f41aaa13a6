/* Professional Theme for Fuel Storage System */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
    --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
    --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
    
    --border-radius: 15px;
    --border-radius-small: 10px;
    --border-radius-large: 20px;
    
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Enhanced Cards */
.card-enhanced {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    border: none;
    overflow: hidden;
    transition: var(--transition);
}

.card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

/* Gradient Buttons */
.btn-gradient-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-gradient-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-gradient-success {
    background: var(--success-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(67, 233, 123, 0.3);
}

.btn-gradient-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(67, 233, 123, 0.4);
    color: white;
}

.btn-gradient-danger {
    background: var(--danger-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(240, 147, 251, 0.3);
}

.btn-gradient-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
    color: white;
}

/* Enhanced Tables */
.table-enhanced {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    border: none;
}

.table-enhanced thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.table-enhanced tbody tr {
    transition: var(--transition);
    border: none;
}

.table-enhanced tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
}

.table-enhanced tbody td {
    padding: 1.25rem;
    border-top: 1px solid #f1f3f4;
    vertical-align: middle;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-paid {
    background: var(--success-gradient);
    color: white;
}

.status-pending {
    background: var(--warning-gradient);
    color: #8b4513;
}

.status-overdue {
    background: var(--danger-gradient);
    color: white;
}

/* Form Enhancements */
.form-control-enhanced {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-small);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: white;
}

.form-control-enhanced:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-label-enhanced {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label-enhanced i {
    color: #667eea;
}

/* Page Headers */
.page-header-enhanced {
    background: var(--primary-gradient);
    color: white;
    padding: 3rem 0;
    margin: -2rem -2rem 2rem -2rem;
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-title-enhanced {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.page-subtitle-enhanced {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}

/* Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header-enhanced {
        margin: -1rem -1rem 1.5rem -1rem;
        padding: 2rem 0;
    }
    
    .page-title-enhanced {
        font-size: 2rem;
    }
    
    .btn-gradient-primary,
    .btn-gradient-success,
    .btn-gradient-danger {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .page-header-enhanced {
        background: var(--primary-gradient) !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .card-enhanced {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
