from django.core.management.base import BaseCommand
from fuel_storage.models import Account

class Command(BaseCommand):
    help = 'إنشاء الحسابات المحاسبية الأساسية'

    def handle(self, *args, **options):
        accounts = [
            # الأصول
            {'code': '1000', 'name': 'الأصول', 'account_type': 'asset', 'parent': None},
            {'code': '1100', 'name': 'النقدية والبنوك', 'account_type': 'asset', 'parent': '1000'},
            {'code': '1110', 'name': 'النقدية', 'account_type': 'asset', 'parent': '1100'},
            {'code': '1120', 'name': 'البنوك', 'account_type': 'asset', 'parent': '1100'},
            {'code': '1200', 'name': 'العملاء', 'account_type': 'asset', 'parent': '1000'},
            {'code': '1300', 'name': 'المخزون', 'account_type': 'asset', 'parent': '1000'},
            
            # الخصوم
            {'code': '2000', 'name': 'الخصوم', 'account_type': 'liability', 'parent': None},
            {'code': '2100', 'name': 'الموردين', 'account_type': 'liability', 'parent': '2000'},
            {'code': '2200', 'name': 'ضريبة القيمة المضافة', 'account_type': 'liability', 'parent': '2000'},
            
            # حقوق الملكية
            {'code': '3000', 'name': 'حقوق الملكية', 'account_type': 'equity', 'parent': None},
            {'code': '3100', 'name': 'رأس المال', 'account_type': 'equity', 'parent': '3000'},
            
            # الإيرادات
            {'code': '4000', 'name': 'الإيرادات', 'account_type': 'revenue', 'parent': None},
            {'code': '4100', 'name': 'المبيعات', 'account_type': 'revenue', 'parent': '4000'},
            
            # المصروفات
            {'code': '5000', 'name': 'المصروفات', 'account_type': 'expense', 'parent': None},
            {'code': '5100', 'name': 'تكلفة البضاعة المباعة', 'account_type': 'expense', 'parent': '5000'},
            {'code': '5200', 'name': 'مصروفات التشغيل', 'account_type': 'expense', 'parent': '5000'},
        ]
        
        created_accounts = {}
        
        for account_data in accounts:
            parent_code = account_data.pop('parent')
            parent = created_accounts.get(parent_code) if parent_code else None
            
            account, created = Account.objects.get_or_create(
                code=account_data['code'],
                defaults={
                    **account_data,
                    'parent': parent
                }
            )
            
            created_accounts[account.code] = account
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء الحساب: {account.code} - {account.name}')
                )
            else:
                self.stdout.write(f'الحساب موجود مسبق<|im_start|>: {account.code} - {account.name}')
        
        self.stdout.write(
            self.style.SUCCESS('تم إنشاء جميع الحسابات الأساسية بنجاح')
        )