{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الفاتورة - {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: 0 0 20px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
    }

    .invoice-detail-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
        border: none;
    }

    .invoice-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 2rem;
        border-bottom: 1px solid #dee2e6;
        text-align: center;
        position: relative;
    }

    .invoice-number {
        font-size: 3rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .invoice-type-badge {
        display: inline-block;
        padding: 0.75rem 2rem;
        border-radius: 30px;
        font-size: 1.1rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        margin-bottom: 1rem;
    }

    .type-sales {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .type-purchase {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .type-return_sales {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #8b4513;
    }

    .type-return_purchase {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #6b46c1;
    }

    .invoice-amount {
        font-size: 3.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 1rem 0;
        text-align: center;
        margin: 30px 0;
        color: #2c5aa0;
    }
    
    .detail-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .detail-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .section-title i {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        font-size: 1.1rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .detail-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
    }

    .detail-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .detail-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .detail-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-label i {
        color: #667eea;
    }

    .detail-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .items-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 10px;
        text-align: center;
        font-weight: 600;
    }
    
    .items-table td {
        padding: 12px 10px;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    
    .items-table tbody tr:nth-child(even) {
        background: #f8f9fa;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn-action {
        padding: 12px 25px;
        border-radius: 10px;
        border: none;
        font-weight: bold;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-print {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
    }
    
    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        color: white;
    }
    
    .btn-back:hover {
        background: #5a6268;
        color: white;
    }
    
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-draft { background: #ffeaa7; color: #d63031; }
    .status-sent { background: #74b9ff; color: #0984e3; }
    .status-paid { background: #00b894; color: white; }
    .status-partially-paid { background: #fdcb6e; color: #e17055; }
    .status-overdue { background: #e17055; color: white; }
    .status-cancelled { background: #636e72; color: white; }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .invoice-detail-container {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header no-print">
    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-file-invoice me-3"></i>
            تفاصيل الفاتورة
        </h1>
        <p class="page-subtitle">عرض تفاصيل شاملة للفاتورة رقم {{ invoice.invoice_number }}</p>
    </div>
</div>

<div class="container-fluid">
    <!-- Quick Actions -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="{% url 'fuel_storage:invoices_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة الفواتير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <!-- Action buttons will be added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Details -->
    <div class="invoice-detail-container">
        <div class="invoice-header">
            <div class="invoice-number">{{ invoice.invoice_number }}</div>
            <span class="invoice-type-badge type-{{ invoice.invoice_type }}">
                {{ invoice.get_invoice_type_display }}
            </span>
            <div class="mt-2">
                <span class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </span>
            </div>
        </div>

        <!-- Amount -->
        <div class="invoice-amount">
            {{ invoice.total_amount|floatformat:2 }} ريال
        </div>

        <!-- Basic Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">تاريخ الفاتورة</div>
                    <div class="detail-value">{{ invoice.invoice_date|date:"j F Y" }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">تاريخ الاستحقاق</div>
                    <div class="detail-value">{{ invoice.due_date|date:"j F Y" }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">المبلغ المدفوع</div>
                    <div class="detail-value">{{ invoice.paid_amount|floatformat:2 }} ريال</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">المبلغ المتبقي</div>
                    <div class="detail-value">{{ invoice.remaining_amount|floatformat:2 }} ريال</div>
                </div>
            </div>
        </div>

        <!-- Customer/Supplier Information -->
        {% if invoice.supplier or invoice.beneficiary %}
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-user"></i>
                {% if invoice.supplier %}معلومات المورد{% else %}معلومات العميل{% endif %}
            </div>
            
            <div class="detail-grid">
                {% if invoice.supplier %}
                    <div class="detail-item">
                        <div class="detail-label">اسم المورد</div>
                        <div class="detail-value">{{ invoice.supplier.full_name }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف</div>
                        <div class="detail-value">{{ invoice.supplier.phone_number|default:"غير محدد" }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">البريد الإلكتروني</div>
                        <div class="detail-value">{{ invoice.supplier.email|default:"غير محدد" }}</div>
                    </div>
                {% elif invoice.beneficiary %}
                    <div class="detail-item">
                        <div class="detail-label">اسم العميل</div>
                        <div class="detail-value">{{ invoice.beneficiary.full_name }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف</div>
                        <div class="detail-value">{{ invoice.beneficiary.phone_number|default:"غير محدد" }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">البريد الإلكتروني</div>
                        <div class="detail-value">{{ invoice.beneficiary.email|default:"غير محدد" }}</div>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Invoice Items -->
        {% if items %}
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-list"></i>
                بنود الفاتورة
            </div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الصنف</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>الخصم %</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.category.name }}</td>
                        <td>{{ item.description|default:"بدون وصف" }}</td>
                        <td>{{ item.quantity|floatformat:2 }}</td>
                        <td>{{ item.unit_price|floatformat:2 }}</td>
                        <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                        <td>{{ item.total_amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- Additional Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-user-cog"></i>
                معلومات إضافية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">أنشئت بواسطة</div>
                    <div class="detail-value">{{ invoice.created_by.full_name }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">تاريخ الإنشاء</div>
                    <div class="detail-value">{{ invoice.created_at|date:"j F Y - H:i" }}</div>
                </div>
                
                {% if invoice.notes %}
                <div class="detail-item" style="grid-column: 1 / -1;">
                    <div class="detail-label">ملاحظات</div>
                    <div class="detail-value">{{ invoice.notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons no-print">
            <a href="{% url 'fuel_storage:print_invoice_simple' invoice.id %}" target="_blank" class="btn-action btn-print">
                <i class="fas fa-print"></i>
                طباعة الفاتورة
            </a>

            <a href="{% url 'admin:fuel_storage_invoice_change' invoice.id %}" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>

            <a href="{% url 'fuel_storage:invoices_list' %}" class="btn-action btn-back">
                <i class="fas fa-arrow-right"></i>
                العودة
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للعناصر
        const detailItems = document.querySelectorAll('.detail-item');
        detailItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `تفاصيل الفاتورة - {{ invoice.invoice_number }}`;
    });
</script>
{% endblock %}
